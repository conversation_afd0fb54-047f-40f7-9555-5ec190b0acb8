---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: tcpingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: TCPIngress
    listKind: TCPIngressList
    plural: tcpingresses
    singular: tcpingress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address of the load balancer
      jsonPath: .status.loadBalancer.ingress[*].ip
      name: Address
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    deprecationWarning: configuration.konghq.com/v1beta1 TCPIngress is deprecated
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: |-
          TCPIngress is the Schema for the tcpingresses API.
          Deprecated: Use Gateway API instead.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the TCPIngress specification.
            properties:
              rules:
                description: A list of rules used to configure the Ingress.
                items:
                  description: |-
                    IngressRule represents a rule to apply against incoming requests.
                    Matching is performed based on an (optional) SNI and port.
                  properties:
                    backend:
                      description: |-
                        Backend defines the referenced service endpoint to which the traffic
                        will be forwarded to.
                      properties:
                        serviceName:
                          description: Specifies the name of the referenced service.
                          minLength: 1
                          type: string
                        servicePort:
                          description: Specifies the port of the referenced service.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - serviceName
                      - servicePort
                      type: object
                    host:
                      description: |-
                        Host is the fully qualified domain name of a network host, as defined
                        by RFC 3986.
                        If a Host is not specified, then port-based TCP routing is performed. Kong
                        doesn't care about the content of the TCP stream in this case.
                        If a Host is specified, the protocol must be TLS over TCP.
                        A plain-text TCP request cannot be routed based on Host. It can only
                        be routed based on Port.
                      type: string
                    port:
                      description: |-
                        Port is the port on which to accept TCP or TLS over TCP sessions and
                        route. It is a required field. If a Host is not specified, the requested
                        are routed based only on Port.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                  required:
                  - backend
                  - port
                  type: object
                type: array
              tls:
                description: |-
                  TLS configuration. This is similar to the `tls` section in the
                  Ingress resource in networking.v1beta1 group.
                  The mapping of SNIs to TLS cert-key pair defined here will be
                  used for HTTP Ingress rules as well. Once can define the mapping in
                  this resource or the original Ingress resource, both have the same
                  effect.
                items:
                  description: IngressTLS describes the transport layer security.
                  properties:
                    hosts:
                      description: |-
                        Hosts are a list of hosts included in the TLS certificate. The values in
                        this list must match the name/s used in the tlsSecret. Defaults to the
                        wildcard host setting for the loadbalancer controller fulfilling this
                        Ingress, if left unspecified.
                      items:
                        type: string
                      type: array
                    secretName:
                      description: SecretName is the name of the secret used to terminate
                        SSL traffic.
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: TCPIngressStatus defines the observed state of TCPIngress.
            properties:
              loadBalancer:
                description: LoadBalancer contains the current status of the load-balancer.
                properties:
                  ingress:
                    description: |-
                      Ingress is a list containing ingress points for the load-balancer.
                      Traffic intended for the service should be sent to these ingress points.
                    items:
                      description: |-
                        LoadBalancerIngress represents the status of a load-balancer ingress point:
                        traffic intended for the service should be sent to an ingress point.
                      properties:
                        hostname:
                          description: |-
                            Hostname is set for load-balancer ingress points that are DNS based
                            (typically AWS load-balancers)
                          type: string
                        ip:
                          description: |-
                            IP is set for load-balancer ingress points that are IP based
                            (typically GCE or OpenStack load-balancers)
                          type: string
                        ipMode:
                          description: |-
                            IPMode specifies how the load-balancer IP behaves, and may only be specified when the ip field is specified.
                            Setting this to "VIP" indicates that traffic is delivered to the node with
                            the destination set to the load-balancer's IP and port.
                            Setting this to "Proxy" indicates that traffic is delivered to the node or pod with
                            the destination set to the node's IP and node port or the pod's IP and port.
                            Service implementations may use this information to adjust traffic routing.
                          type: string
                        ports:
                          description: |-
                            Ports is a list of records of service ports
                            If used, every port defined in the service should have an entry in it
                          items:
                            description: PortStatus represents the error condition
                              of a service port
                            properties:
                              error:
                                description: |-
                                  Error is to record the problem with the service port
                                  The format of the error shall comply with the following rules:
                                  - built-in error values shall be specified in this file and those shall use
                                    CamelCase names
                                  - cloud provider specific error values must have names that comply with the
                                    format foo.example.com/CamelCase.
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                              port:
                                description: Port is the port number of the service
                                  port of which status is recorded here
                                format: int32
                                type: integer
                              protocol:
                                description: |-
                                  Protocol is the protocol of the service port of which status is recorded here
                                  The supported values are: "TCP", "UDP", "SCTP"
                                type: string
                            required:
                            - error
                            - port
                            - protocol
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
