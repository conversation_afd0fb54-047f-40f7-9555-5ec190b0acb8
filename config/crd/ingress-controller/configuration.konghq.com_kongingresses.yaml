---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongIngress
    listKind: KongIngressList
    plural: kongingresses
    shortNames:
    - ki
    singular: kongingress
  scope: Namespaced
  versions:
  - deprecated: true
    deprecationWarning: configuration.konghq.com/v1 KongIngress is deprecated
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          KongIngress is the Schema for the kongingresses API.
          Deprecated: Use Gateway API instead.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          proxy:
            description: |-
              Proxy defines additional connection options for the routes to be configured in the
              Kong Gateway, e.g. `connection_timeout`, `retries`, etc.
            properties:
              connect_timeout:
                description: "The timeout in milliseconds for\testablishing a connection
                  to the upstream server.\nDeprecated: use Service's \"konghq.com/connect-timeout\"
                  annotation instead."
                minimum: 0
                type: integer
              path:
                description: |-
                  (optional) The path to be used in requests to the upstream server.
                  Deprecated: use Service's "konghq.com/path" annotation instead.
                pattern: ^/.*$
                type: string
              protocol:
                description: |-
                  The protocol used to communicate with the upstream.
                  Deprecated: use Service's "konghq.com/protocol" annotation instead.
                enum:
                - http
                - https
                - grpc
                - grpcs
                - tcp
                - tls
                - udp
                type: string
              read_timeout:
                description: |-
                  The timeout in milliseconds between two successive read operations
                  for transmitting a request to the upstream server.
                  Deprecated: use Service's "konghq.com/read-timeout" annotation instead.
                minimum: 0
                type: integer
              retries:
                description: |-
                  The number of retries to execute upon failure to proxy.
                  Deprecated: use Service's "konghq.com/retries" annotation instead.
                minimum: 0
                type: integer
              write_timeout:
                description: |-
                  The timeout in milliseconds between two successive write operations
                  for transmitting a request to the upstream server.
                  Deprecated: use Service's "konghq.com/write-timeout" annotation instead.
                minimum: 0
                type: integer
            type: object
          route:
            description: |-
              Route define rules to match client requests.
              Each Route is associated with a Service,
              and a Service may have multiple Routes associated to it.
            properties:
              headers:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: |-
                  Headers contains one or more lists of values indexed by header name
                  that will cause this Route to match if present in the request.
                  The Host header cannot be used with this attribute.
                  Deprecated: use Ingress' "konghq.com/headers" annotation instead.
                type: object
              https_redirect_status_code:
                description: |-
                  HTTPSRedirectStatusCode is the status code Kong responds with
                  when all properties of a Route match except the protocol.
                  Deprecated: use Ingress' "ingress.kubernetes.io/force-ssl-redirect" or
                  "konghq.com/https-redirect-status-code" annotations instead.
                type: integer
              methods:
                description: |-
                  Methods is a list of HTTP methods that match this Route.
                  Deprecated: use Ingress' "konghq.com/methods" annotation instead.
                items:
                  type: string
                type: array
              path_handling:
                description: |-
                  PathHandling controls how the Service path, Route path and requested path
                  are combined when sending a request to the upstream.
                  Deprecated: use Ingress' "konghq.com/path-handling" annotation instead.
                enum:
                - v0
                - v1
                type: string
              preserve_host:
                description: |-
                  PreserveHost sets When matching a Route via one of the hosts domain names,
                  use the request Host header in the upstream request headers.
                  If set to false, the upstream Host header will be that of the Service’s host.
                  Deprecated: use Ingress' "konghq.com/preserve-host" annotation instead.
                type: boolean
              protocols:
                description: |-
                  Protocols is an array of the protocols this Route should allow.
                  Deprecated: use Ingress' "konghq.com/protocols" annotation instead.
                items:
                  description: |-
                    KongProtocol is a valid Kong protocol.
                    This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
                  enum:
                  - http
                  - https
                  - grpc
                  - grpcs
                  - tcp
                  - tls
                  - udp
                  type: string
                type: array
              regex_priority:
                description: |-
                  RegexPriority is a number used to choose which route resolves a given request
                  when several routes match it using regexes simultaneously.
                  Deprecated: use Ingress' "konghq.com/regex-priority" annotation instead.
                type: integer
              request_buffering:
                description: |-
                  RequestBuffering sets whether to enable request body buffering or not.
                  Deprecated: use Ingress' "konghq.com/request-buffering" annotation instead.
                type: boolean
              response_buffering:
                description: |-
                  ResponseBuffering sets whether to enable response body buffering or not.
                  Deprecated: use Ingress' "konghq.com/response-buffering" annotation instead.
                type: boolean
              snis:
                description: |-
                  SNIs is a list of SNIs that match this Route when using stream routing.
                  Deprecated: use Ingress' "konghq.com/snis" annotation instead.
                items:
                  type: string
                type: array
              strip_path:
                description: |-
                  StripPath sets When matching a Route via one of the paths
                  strip the matching prefix from the upstream request URL.
                  Deprecated: use Ingress' "konghq.com/strip-path" annotation instead.
                type: boolean
            type: object
          upstream:
            description: |-
              Upstream represents a virtual hostname and can be used to loadbalance
              incoming requests over multiple targets (e.g. Kubernetes `Services` can
              be a target, OR `Endpoints` can be targets).
            properties:
              algorithm:
                description: |-
                  Algorithm is the load balancing algorithm to use.
                  Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency".
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                type: string
              hash_fallback:
                description: |-
                  HashFallback defines What to use as hashing input
                  if the primary hash_on does not return a hash.
                  Accepted values are: "none", "consumer", "ip", "header", "cookie".
                type: string
              hash_fallback_header:
                description: |-
                  HashFallbackHeader is the header name to take the value from as hash input.
                  Only required when "hash_fallback" is set to "header".
                type: string
              hash_fallback_query_arg:
                description: HashFallbackQueryArg is the "hash_fallback" version of
                  HashOnQueryArg.
                type: string
              hash_fallback_uri_capture:
                description: HashFallbackURICapture is the "hash_fallback" version
                  of HashOnURICapture.
                type: string
              hash_on:
                description: |-
                  HashOn defines what to use as hashing input.
                  Accepted values are: "none", "consumer", "ip", "header", "cookie", "path", "query_arg", "uri_capture".
                type: string
              hash_on_cookie:
                description: |-
                  The cookie name to take the value from as hash input.
                  Only required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_cookie_path:
                description: |-
                  The cookie path to set in the response headers.
                  Only required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_header:
                description: |-
                  HashOnHeader defines the header name to take the value from as hash input.
                  Only required when "hash_on" is set to "header".
                type: string
              hash_on_query_arg:
                description: HashOnQueryArg is the query string parameter whose value
                  is the hash input when "hash_on" is set to "query_arg".
                type: string
              hash_on_uri_capture:
                description: |-
                  HashOnURICapture is the name of the capture group whose value is the hash input when "hash_on" is set to
                  "uri_capture".
                type: string
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: ActiveHealthcheck configures active health check
                      probing.
                    properties:
                      concurrency:
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        type: object
                      healthy:
                        description: |-
                          Healthy configures thresholds and HTTP status codes
                          to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      http_path:
                        pattern: ^/.*$
                        type: string
                      https_sni:
                        type: string
                      https_verify_certificate:
                        type: boolean
                      timeout:
                        minimum: 0
                        type: integer
                      type:
                        type: string
                      unhealthy:
                        description: |-
                          Unhealthy configures thresholds and HTTP status codes
                          to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: |-
                      PassiveHealthcheck configures passive checks around
                      passive health checks.
                    properties:
                      healthy:
                        description: |-
                          Healthy configures thresholds and HTTP status codes
                          to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        type: string
                      unhealthy:
                        description: |-
                          Unhealthy configures thresholds and HTTP status codes
                          to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    type: number
                type: object
              host_header:
                description: |-
                  HostHeader is The hostname to be used as Host header
                  when proxying requests through Kong.
                type: string
              slots:
                description: Slots is the number of slots in the load balancer algorithm.
                minimum: 10
                type: integer
            type: object
        type: object
        x-kubernetes-validations:
        - message: '''proxy'' field is no longer supported, use Service''s annotations
            instead'
          rule: '!has(self.proxy)'
        - message: '''route'' field is no longer supported, use Ingress'' annotations
            instead'
          rule: '!has(self.route)'
    served: true
    storage: true
    subresources:
      status: {}
