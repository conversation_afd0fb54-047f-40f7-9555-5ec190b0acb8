---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konnectextensions.konnect.konghq.com
spec:
  group: konnect.konghq.com
  names:
    categories:
    - kong
    kind: KonnectExtension
    listKind: KonnectExtensionList
    plural: konnectextensions
    singular: konnectextension
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Ready to be used
      jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    deprecated: true
    deprecationWarning: This API has been deprecated in favor of v1alpha2 konnectextensions.konnect.konghq.com
      and it will be removed in future version.
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
          extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
          If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
          deployment(s) spec gets customized to include the konnect-related configuration.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the specification of the KonnectExtension resource.
            properties:
              clientAuth:
                default:
                  certificateSecret:
                    provisioning: Automatic
                description: |-
                  ClientAuth is the configuration for the client certificate authentication.
                  In case the ControlPlaneRef is of type KonnectID, it is required to set up the connection with the
                  Konnect Platform.
                properties:
                  certificateSecret:
                    description: CertificateSecret contains the information to access
                      the client certificate.
                    properties:
                      provisioning:
                        default: Automatic
                        description: |-
                          Provisioning is the method used to provision the certificate. It can be either Manual or Automatic.
                          In case manual provisioning is used, the certificate must be provided by the user.
                          In case automatic provisioning is used, the certificate will be automatically generated by the system.
                        enum:
                        - Manual
                        - Automatic
                        type: string
                      secretRef:
                        description: CertificateSecretRef is the reference to the
                          Secret containing the client certificate.
                        properties:
                          name:
                            description: Name is the name of the Secret containing
                              the Konnect Control Plane's cluster certificate.
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                required:
                - certificateSecret
                type: object
                x-kubernetes-validations:
                - message: secretRef must be set when provisioning is set to Manual.
                  rule: 'self.certificateSecret.provisioning == ''Manual'' ? has(self.certificateSecret.secretRef)
                    : true'
                - message: secretRef must not be set when provisioning is set to Automatic.
                  rule: 'self.certificateSecret.provisioning == ''Automatic'' ? !has(self.certificateSecret.secretRef)
                    : true'
              konnect:
                description: Konnect holds the konnect-related configuration
                properties:
                  configuration:
                    description: Configuration holds the information needed to set
                      up the Konnect Configuration.
                    properties:
                      authRef:
                        description: |-
                          APIAuthConfigurationRef is the reference to the API Auth Configuration
                          that should be used for this Konnect Configuration.
                        properties:
                          name:
                            description: Name is the name of the KonnectAPIAuthConfiguration
                              resource.
                            type: string
                        required:
                        - name
                        type: object
                    required:
                    - authRef
                    type: object
                  controlPlane:
                    description: ControlPlane is the configuration for the Konnect
                      Control Plane.
                    properties:
                      ref:
                        description: Ref is a reference to a Konnect ControlPlane
                          this KonnectExtension is associated with.
                        properties:
                          konnectID:
                            description: |-
                              KonnectID is the schema for the KonnectID type.
                              This field is required when the Type is konnectID.
                            pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                            type: string
                          konnectNamespacedRef:
                            description: |-
                              KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                              It contains the name of the Konnect Control Plane.
                              This field is required when the Type is konnectNamespacedRef.
                            properties:
                              name:
                                description: Name is the name of the Konnect Control
                                  Plane.
                                type: string
                              namespace:
                                description: |-
                                  Namespace is the namespace where the Konnect Control Plane is in.
                                  Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                                type: string
                            required:
                            - name
                            type: object
                          type:
                            default: kic
                            description: |-
                              Type indicates the type of the control plane being referenced. Allowed values:
                              - konnectID
                              - konnectNamespacedRef
                              - kic

                              The default is kic, which implies that the Control Plane is KIC.
                            enum:
                            - konnectID
                            - konnectNamespacedRef
                            - kic
                            type: string
                        type: object
                        x-kubernetes-validations:
                        - message: kic type not supported as controlPlaneRef.
                          rule: self.type != 'kic'
                        - message: when type is konnectNamespacedRef, konnectNamespacedRef
                            must be set
                          rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                            ? has(self.konnectNamespacedRef) : true'
                        - message: when type is konnectNamespacedRef, konnectID must
                            not be set
                          rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                            ? !has(self.konnectID) : true'
                        - message: when type is konnectID, konnectID must be set
                          rule: '(has(self.type) && self.type == ''konnectID'') ?
                            has(self.konnectID) : true'
                        - message: when type is konnectID, konnectNamespacedRef must
                            not be set
                          rule: '(has(self.type) && self.type == ''konnectID'') ?
                            !has(self.konnectNamespacedRef) : true'
                        - message: when type is kic, konnectID must not be set
                          rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                            : true'
                        - message: when type is kic, konnectNamespacedRef must not
                            be set
                          rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                            : true'
                        - message: when type is unset, konnectID must not be set
                          rule: '!has(self.type) ? !has(self.konnectID) : true'
                        - message: when type is unset, konnectNamespacedRef must not
                            be set
                          rule: '!has(self.type) ? !has(self.konnectNamespacedRef)
                            : true'
                    required:
                    - ref
                    type: object
                  dataPlane:
                    description: DataPlane is the configuration for the Konnect DataPlane.
                    properties:
                      labels:
                        additionalProperties:
                          description: DataPlaneLabelValue is the type that defines
                            the value of a label that will be applied to the Konnect
                            DataPlane.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$
                          type: string
                        description: Labels is a set of labels that will be applied
                          to the Konnect DataPlane.
                        type: object
                        x-kubernetes-validations:
                        - message: keys must match the pattern '^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$'.
                          rule: self.all(key, key.matches('^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$'))
                        - message: keys must not start with 'kong', 'konnect', 'insomnia',
                            'mesh', 'kic', or '_'.
                          rule: self.all(key, !(key.startsWith('kong') || key.startsWith('konnect')
                            || key.startsWith('insomnia') || key.startsWith('mesh')
                            || key.startsWith('kic') || key.startsWith('_')))
                        - message: 'Too long: may not be more than 63 bytes'
                          rule: self.all(key, size(key) > 0 && size(key) < 64)
                    type: object
                required:
                - controlPlane
                type: object
            required:
            - konnect
            type: object
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Ready
            description: Status is the status of the KonnectExtension resource.
            properties:
              conditions:
                description: |-
                  Conditions describe the current conditions of the KonnectExtensionStatus.
                  Known condition types are:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              controlPlaneRefs:
                description: |-
                  ControlPlaneRefs is the array  of ControlPlane references this is associated with.
                  A new reference is set by the operator when this extension is associated with
                  a ControlPlane through its extensions spec.
                items:
                  description: NamespacedRef is a reference to a namespaced resource.
                  properties:
                    name:
                      description: Name is the name of the referred resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    namespace:
                      description: |-
                        Namespace is the namespace of the referred resource.

                        For namespace-scoped resources if no Namespace is provided then the
                        namespace of the parent object MUST be used.

                        This field MUST not be set when referring to cluster-scoped resources.
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 16
                type: array
              dataPlaneClientAuth:
                description: DataPlaneClientAuth contains the configuration for the
                  client certificate authentication for the DataPlane.
                properties:
                  certificateSecretRef:
                    description: CertificateSecretRef is the reference to the Secret
                      containing the client certificate.
                    properties:
                      name:
                        description: Name is the name of the Secret containing the
                          Konnect Control Plane's cluster certificate.
                        type: string
                    required:
                    - name
                    type: object
                type: object
              dataPlaneRefs:
                description: |-
                  DataPlaneRefs is the array  of DataPlane references this is associated with.
                  A new reference is set by the operator when this extension is associated with
                  a DataPlane through its extensions spec.
                items:
                  description: NamespacedRef is a reference to a namespaced resource.
                  properties:
                    name:
                      description: Name is the name of the referred resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    namespace:
                      description: |-
                        Namespace is the namespace of the referred resource.

                        For namespace-scoped resources if no Namespace is provided then the
                        namespace of the parent object MUST be used.

                        This field MUST not be set when referring to cluster-scoped resources.
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 16
                type: array
              konnect:
                description: Konnect contains the status information related to the
                  Konnect Control Plane.
                properties:
                  clusterType:
                    description: ClusterType is the type of the Konnect Control Plane.
                    enum:
                    - ControlPlane
                    - K8SIngressController
                    type: string
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this KonnectExtension is associated with.
                    type: string
                  endpoints:
                    description: Endpoints defines the Konnect endpoints for the control
                      plane.
                    properties:
                      controlPlane:
                        description: ControlPlaneEndpoint is the endpoint for the
                          control plane.
                        type: string
                      telemetry:
                        description: TelemetryEndpoint is the endpoint for telemetry.
                        type: string
                    required:
                    - controlPlane
                    - telemetry
                    type: object
                required:
                - clusterType
                - controlPlaneID
                - endpoints
                type: object
            type: object
        type: object
        x-kubernetes-validations:
        - message: spec.konnect.controlPlane.ref is immutable.
          rule: oldSelf.spec.konnect.controlPlane.ref == self.spec.konnect.controlPlane.ref
        - message: konnect must be set when ControlPlaneRef is set to KonnectID.
          rule: 'self.spec.konnect.controlPlane.ref.type == ''konnectID'' ? has(self.spec.konnect.configuration)
            : true'
        - message: konnect must be unset when ControlPlaneRef is set to konnectNamespacedRef.
          rule: 'self.spec.konnect.controlPlane.ref.type == ''konnectNamespacedRef''
            ? !has(self.spec.konnect.configuration) : true'
    served: false
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The Resource is Ready to be used
      jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    name: v1alpha2
    schema:
      openAPIV3Schema:
        description: |-
          KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
          extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
          If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
          deployment(s) spec gets customized to include the konnect-related configuration.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the specification of the KonnectExtension resource.
            properties:
              clientAuth:
                default:
                  certificateSecret:
                    provisioning: Automatic
                description: ClientAuth is the configuration for the client certificate
                  authentication.
                properties:
                  certificateSecret:
                    description: CertificateSecret contains the information to access
                      the client certificate.
                    properties:
                      provisioning:
                        default: Automatic
                        description: |-
                          Provisioning is the method used to provision the certificate. It can be either Manual or Automatic.
                          In case manual provisioning is used, the certificate must be provided by the user.
                          In case automatic provisioning is used, the certificate will be automatically generated by the system.
                        enum:
                        - Manual
                        - Automatic
                        type: string
                      secretRef:
                        description: CertificateSecretRef is the reference to the
                          Secret containing the client certificate.
                        properties:
                          name:
                            description: Name is the name of the Secret containing
                              the Konnect Control Plane's cluster certificate.
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                required:
                - certificateSecret
                type: object
                x-kubernetes-validations:
                - message: secretRef must be set when provisioning is set to Manual.
                  rule: 'self.certificateSecret.provisioning == ''Manual'' ? has(self.certificateSecret.secretRef)
                    : true'
                - message: secretRef must not be set when provisioning is set to Automatic.
                  rule: 'self.certificateSecret.provisioning == ''Automatic'' ? !has(self.certificateSecret.secretRef)
                    : true'
              konnect:
                description: Konnect holds the konnect-related configuration
                properties:
                  controlPlane:
                    description: ControlPlane is the configuration for the Konnect
                      Control Plane.
                    properties:
                      ref:
                        description: Ref is a reference to a Konnect ControlPlane
                          this KonnectExtension is associated with.
                        properties:
                          konnectNamespacedRef:
                            description: |-
                              KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                              It contains the name of the Konnect Control Plane.
                              This field is required when the Type is konnectNamespacedRef.
                            properties:
                              name:
                                description: Name is the name of the Konnect Control
                                  Plane.
                                type: string
                              namespace:
                                description: |-
                                  Namespace is the namespace where the Konnect Control Plane is in.
                                  Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                                type: string
                            required:
                            - name
                            type: object
                          type:
                            default: konnectNamespacedRef
                            description: |-
                              Type indicates the type of the control plane being referenced. Allowed values:
                              - konnectNamespacedRef

                              The default is kic, which implies that the Control Plane is KIC.
                            enum:
                            - konnectNamespacedRef
                            type: string
                        type: object
                        x-kubernetes-validations:
                        - message: when type is konnectNamespacedRef, konnectNamespacedRef
                            must be set
                          rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                            ? has(self.konnectNamespacedRef) : true'
                    required:
                    - ref
                    type: object
                  dataPlane:
                    description: DataPlane is the configuration for the Konnect DataPlane.
                    properties:
                      labels:
                        additionalProperties:
                          description: DataPlaneLabelValue is the type that defines
                            the value of a label that will be applied to the Konnect
                            DataPlane.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$
                          type: string
                        description: Labels is a set of labels that will be applied
                          to the Konnect DataPlane.
                        type: object
                        x-kubernetes-validations:
                        - message: keys must match the pattern '^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$'.
                          rule: self.all(key, key.matches('^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$'))
                        - message: keys must not start with 'kong', 'konnect', 'insomnia',
                            'mesh', 'kic', or '_'.
                          rule: self.all(key, !(key.startsWith('kong') || key.startsWith('konnect')
                            || key.startsWith('insomnia') || key.startsWith('mesh')
                            || key.startsWith('kic') || key.startsWith('_')))
                        - message: 'Too long: may not be more than 63 bytes'
                          rule: self.all(key, size(key) > 0 && size(key) < 64)
                    type: object
                required:
                - controlPlane
                type: object
            required:
            - konnect
            type: object
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Ready
            description: Status is the status of the KonnectExtension resource.
            properties:
              conditions:
                description: |-
                  Conditions describe the current conditions of the KonnectExtensionStatus.
                  Known condition types are:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              controlPlaneRefs:
                description: |-
                  ControlPlaneRefs is the array  of ControlPlane references this is associated with.
                  A new reference is set by the operator when this extension is associated with
                  a ControlPlane through its extensions spec.
                items:
                  description: NamespacedRef is a reference to a namespaced resource.
                  properties:
                    name:
                      description: Name is the name of the referred resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    namespace:
                      description: |-
                        Namespace is the namespace of the referred resource.

                        For namespace-scoped resources if no Namespace is provided then the
                        namespace of the parent object MUST be used.

                        This field MUST not be set when referring to cluster-scoped resources.
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 16
                type: array
              dataPlaneClientAuth:
                description: DataPlaneClientAuth contains the configuration for the
                  client certificate authentication for the DataPlane.
                properties:
                  certificateSecretRef:
                    description: CertificateSecretRef is the reference to the Secret
                      containing the client certificate.
                    properties:
                      name:
                        description: Name is the name of the Secret containing the
                          Konnect Control Plane's cluster certificate.
                        type: string
                    required:
                    - name
                    type: object
                type: object
              dataPlaneRefs:
                description: |-
                  DataPlaneRefs is the array  of DataPlane references this is associated with.
                  A new reference is set by the operator when this extension is associated with
                  a DataPlane through its extensions spec.
                items:
                  description: NamespacedRef is a reference to a namespaced resource.
                  properties:
                    name:
                      description: Name is the name of the referred resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    namespace:
                      description: |-
                        Namespace is the namespace of the referred resource.

                        For namespace-scoped resources if no Namespace is provided then the
                        namespace of the parent object MUST be used.

                        This field MUST not be set when referring to cluster-scoped resources.
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 16
                type: array
              konnect:
                description: Konnect contains the status information related to the
                  Konnect Control Plane.
                properties:
                  clusterType:
                    description: ClusterType is the type of the Konnect Control Plane.
                    enum:
                    - ControlPlane
                    - K8SIngressController
                    type: string
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this KonnectExtension is associated with.
                    type: string
                  endpoints:
                    description: Endpoints defines the Konnect endpoints for the control
                      plane.
                    properties:
                      controlPlane:
                        description: ControlPlaneEndpoint is the endpoint for the
                          control plane.
                        type: string
                      telemetry:
                        description: TelemetryEndpoint is the endpoint for telemetry.
                        type: string
                    required:
                    - controlPlane
                    - telemetry
                    type: object
                required:
                - clusterType
                - controlPlaneID
                - endpoints
                type: object
            type: object
        type: object
        x-kubernetes-validations:
        - message: spec.konnect.controlPlane.ref is immutable.
          rule: oldSelf.spec.konnect.controlPlane.ref == self.spec.konnect.controlPlane.ref
    served: true
    storage: true
    subresources:
      status: {}
