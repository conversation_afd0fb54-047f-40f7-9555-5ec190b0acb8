name: Run tests
run-name: tests, branch:${{ github.ref_name }}, triggered by @${{ github.actor }}

concurrency:
  # Run only for most recent commit in PRs but for all tags and commits on main
  # Ref: https://docs.github.com/en/actions/using-jobs/using-concurrency
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - 'main'
      - 'release/*'
  push:
    branches:
      - 'main'
      - 'release/*'
    tags:
      - '*'
  workflow_dispatch: {}
  workflow_call: {}

permissions:
  contents: read

jobs:
  version:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - id: version
        run: |
          echo "version=$(head -1 VERSION)" >> "$GITHUB_OUTPUT"
      - name: Validate VERSION
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ steps.version.outputs.version }}
          version_extractor_regex: 'v(.*)$'

  ensure-actions-sha-pin:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - uses: zgosalvez/github-actions-ensure-sha-pinned-actions@fc87bb5b5a97953d987372e74478de634726b3e5 # v3.0.25

  actionlint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Run lint.actions
      run: make lint.actions

  test-unit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Run tests
      run: make test.unit.pretty

  generate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Generate
      run: make generate

    - name: Verify
      run: make verify.diff

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - run: make lint

  kube-api-linter:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - run: make lint.api

  matrix_k8s_node_images:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - id: set-matrix
        run: |
          (
            echo 'matrix<<EOF'
            yq eval -o=json '.' .github/supported_k8s_node_images.yaml
            echo 'EOF'
          ) >> "${GITHUB_OUTPUT}"

  apply:
    runs-on: ubuntu-latest
    needs:
    - matrix_k8s_node_images
    strategy:
      matrix:
        include: ${{ fromJson(needs.matrix_k8s_node_images.outputs.matrix) }}

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Create k8s KinD Cluster
      uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0
      with:
        # NOTE: default is 0.26.0 https://github.com/helm/kind-action/blob/a1b0e391336a6ee6713a0583f8c6240d70863de3/kind.sh#L21
        # so bump this manually
        version: v0.27.0
        node_image: ${{ matrix.node_image }}

    - name: Verify installing CRDs via kustomize works
      run: make install.only

    - name: Install and delete each sample one by one
      run: make test.samples

  CRDs-validation:
    runs-on: ubuntu-latest
    needs:
    - matrix_k8s_node_images
    strategy:
      matrix:
        include: ${{ fromJson(needs.matrix_k8s_node_images.outputs.matrix) }}

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Create k8s KinD Cluster
      uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0
      with:
        # NOTE: default is 0.26.0 https://github.com/helm/kind-action/blob/a1b0e391336a6ee6713a0583f8c6240d70863de3/kind.sh#L21
        # so bump this manually
        version: v0.27.0
        node_image: ${{ matrix.node_image }}

    - name: Verify installing CRDs via kustomize works
      run: make install.only

    - name: Run the crds validation tests
      run: make test.crds-validation

  upgrade-crds-from-latest-release:
    runs-on: ubuntu-latest
    needs:
    - matrix_k8s_node_images
    strategy:
      matrix:
        include: ${{ fromJson(needs.matrix_k8s_node_images.outputs.matrix) }}

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
      with:
        install: false

    - name: Create k8s KinD Cluster
      uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0
      with:
        # NOTE: default is 0.26.0 https://github.com/helm/kind-action/blob/a1b0e391336a6ee6713a0583f8c6240d70863de3/kind.sh#L21
        # so bump this manually
        version: v0.27.0
        node_image: ${{ matrix.node_image }}

    - run: make kustomize

    # TODO: Automate bumping the version here.
    - name: Install last release CRDs
      run: kustomize build github.com/kong/kubernetes-configuration/config/crd/gateway-operator\?ref=v1.4.2 | kubectl apply --server-side -f -

    - name: Install current CRDs
      run: make install.only

  # We need this step to fail the workflow if any of the previous steps failed or were cancelled.
  # It allows to use this particular job as a required check for PRs.
  # Ref: https://github.com/orgs/community/discussions/26822#discussioncomment-3305794
  passed:
    runs-on: ubuntu-latest
    needs:
      - ensure-actions-sha-pin
      - version
      - lint
      - kube-api-linter
      - actionlint
      - test-unit
      - generate
      - apply
      - CRDs-validation
      - upgrade-crds-from-latest-release
    if: always()
    steps:
      - if: contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled')
        run: |
          echo "Some jobs failed or were cancelled."
          exit 1
