//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	apisv1 "sigs.k8s.io/gateway-api/apis/v1"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AICloudProvider) DeepCopyInto(out *AICloudProvider) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AICloudProvider.
func (in *AICloudProvider) DeepCopy() *AICloudProvider {
	if in == nil {
		return nil
	}
	out := new(AICloudProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AICloudProviderAPITokenRef) DeepCopyInto(out *AICloudProviderAPITokenRef) {
	*out = *in
	if in.Namespace != nil {
		in, out := &in.Namespace, &out.Namespace
		*out = new(string)
		**out = **in
	}
	if in.Kind != nil {
		in, out := &in.Kind, &out.Kind
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AICloudProviderAPITokenRef.
func (in *AICloudProviderAPITokenRef) DeepCopy() *AICloudProviderAPITokenRef {
	if in == nil {
		return nil
	}
	out := new(AICloudProviderAPITokenRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGateway) DeepCopyInto(out *AIGateway) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGateway.
func (in *AIGateway) DeepCopy() *AIGateway {
	if in == nil {
		return nil
	}
	out := new(AIGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIGateway) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGatewayConsumerRef) DeepCopyInto(out *AIGatewayConsumerRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGatewayConsumerRef.
func (in *AIGatewayConsumerRef) DeepCopy() *AIGatewayConsumerRef {
	if in == nil {
		return nil
	}
	out := new(AIGatewayConsumerRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGatewayEndpoint) DeepCopyInto(out *AIGatewayEndpoint) {
	*out = *in
	if in.AvailableModels != nil {
		in, out := &in.AvailableModels, &out.AvailableModels
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	out.Consumer = in.Consumer
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGatewayEndpoint.
func (in *AIGatewayEndpoint) DeepCopy() *AIGatewayEndpoint {
	if in == nil {
		return nil
	}
	out := new(AIGatewayEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGatewayList) DeepCopyInto(out *AIGatewayList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AIGateway, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGatewayList.
func (in *AIGatewayList) DeepCopy() *AIGatewayList {
	if in == nil {
		return nil
	}
	out := new(AIGatewayList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *AIGatewayList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGatewaySpec) DeepCopyInto(out *AIGatewaySpec) {
	*out = *in
	if in.LargeLanguageModels != nil {
		in, out := &in.LargeLanguageModels, &out.LargeLanguageModels
		*out = new(LargeLanguageModels)
		(*in).DeepCopyInto(*out)
	}
	if in.CloudProviderCredentials != nil {
		in, out := &in.CloudProviderCredentials, &out.CloudProviderCredentials
		*out = new(AICloudProviderAPITokenRef)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGatewaySpec.
func (in *AIGatewaySpec) DeepCopy() *AIGatewaySpec {
	if in == nil {
		return nil
	}
	out := new(AIGatewaySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIGatewayStatus) DeepCopyInto(out *AIGatewayStatus) {
	*out = *in
	if in.Endpoints != nil {
		in, out := &in.Endpoints, &out.Endpoints
		*out = make([]AIGatewayEndpoint, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIGatewayStatus.
func (in *AIGatewayStatus) DeepCopy() *AIGatewayStatus {
	if in == nil {
		return nil
	}
	out := new(AIGatewayStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CloudHostedLargeLanguageModel) DeepCopyInto(out *CloudHostedLargeLanguageModel) {
	*out = *in
	if in.Model != nil {
		in, out := &in.Model, &out.Model
		*out = new(string)
		**out = **in
	}
	if in.PromptType != nil {
		in, out := &in.PromptType, &out.PromptType
		*out = new(LLMPromptType)
		**out = **in
	}
	if in.DefaultPrompts != nil {
		in, out := &in.DefaultPrompts, &out.DefaultPrompts
		*out = make([]LLMPrompt, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DefaultPromptParams != nil {
		in, out := &in.DefaultPromptParams, &out.DefaultPromptParams
		*out = new(LLMPromptParams)
		(*in).DeepCopyInto(*out)
	}
	out.AICloudProvider = in.AICloudProvider
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CloudHostedLargeLanguageModel.
func (in *CloudHostedLargeLanguageModel) DeepCopy() *CloudHostedLargeLanguageModel {
	if in == nil {
		return nil
	}
	out := new(CloudHostedLargeLanguageModel)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterCertificateSecretRef) DeepCopyInto(out *ClusterCertificateSecretRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterCertificateSecretRef.
func (in *ClusterCertificateSecretRef) DeepCopy() *ClusterCertificateSecretRef {
	if in == nil {
		return nil
	}
	out := new(ClusterCertificateSecretRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DataPlaneMetricsExtension) DeepCopyInto(out *DataPlaneMetricsExtension) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DataPlaneMetricsExtension.
func (in *DataPlaneMetricsExtension) DeepCopy() *DataPlaneMetricsExtension {
	if in == nil {
		return nil
	}
	out := new(DataPlaneMetricsExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DataPlaneMetricsExtension) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DataPlaneMetricsExtensionList) DeepCopyInto(out *DataPlaneMetricsExtensionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]DataPlaneMetricsExtension, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DataPlaneMetricsExtensionList.
func (in *DataPlaneMetricsExtensionList) DeepCopy() *DataPlaneMetricsExtensionList {
	if in == nil {
		return nil
	}
	out := new(DataPlaneMetricsExtensionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DataPlaneMetricsExtensionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DataPlaneMetricsExtensionSpec) DeepCopyInto(out *DataPlaneMetricsExtensionSpec) {
	*out = *in
	in.ServiceSelector.DeepCopyInto(&out.ServiceSelector)
	out.Config = in.Config
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DataPlaneMetricsExtensionSpec.
func (in *DataPlaneMetricsExtensionSpec) DeepCopy() *DataPlaneMetricsExtensionSpec {
	if in == nil {
		return nil
	}
	out := new(DataPlaneMetricsExtensionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DataPlaneMetricsExtensionStatus) DeepCopyInto(out *DataPlaneMetricsExtensionStatus) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.NamespacedRef)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DataPlaneMetricsExtensionStatus.
func (in *DataPlaneMetricsExtensionStatus) DeepCopy() *DataPlaneMetricsExtensionStatus {
	if in == nil {
		return nil
	}
	out := new(DataPlaneMetricsExtensionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginInstallation) DeepCopyInto(out *KongPluginInstallation) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginInstallation.
func (in *KongPluginInstallation) DeepCopy() *KongPluginInstallation {
	if in == nil {
		return nil
	}
	out := new(KongPluginInstallation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongPluginInstallation) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginInstallationList) DeepCopyInto(out *KongPluginInstallationList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongPluginInstallation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginInstallationList.
func (in *KongPluginInstallationList) DeepCopy() *KongPluginInstallationList {
	if in == nil {
		return nil
	}
	out := new(KongPluginInstallationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongPluginInstallationList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginInstallationSpec) DeepCopyInto(out *KongPluginInstallationSpec) {
	*out = *in
	if in.ImagePullSecretRef != nil {
		in, out := &in.ImagePullSecretRef, &out.ImagePullSecretRef
		*out = new(apisv1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginInstallationSpec.
func (in *KongPluginInstallationSpec) DeepCopy() *KongPluginInstallationSpec {
	if in == nil {
		return nil
	}
	out := new(KongPluginInstallationSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginInstallationStatus) DeepCopyInto(out *KongPluginInstallationStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginInstallationStatus.
func (in *KongPluginInstallationStatus) DeepCopy() *KongPluginInstallationStatus {
	if in == nil {
		return nil
	}
	out := new(KongPluginInstallationStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectControlPlaneAPIAuthConfiguration) DeepCopyInto(out *KonnectControlPlaneAPIAuthConfiguration) {
	*out = *in
	out.ClusterCertificateSecretRef = in.ClusterCertificateSecretRef
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectControlPlaneAPIAuthConfiguration.
func (in *KonnectControlPlaneAPIAuthConfiguration) DeepCopy() *KonnectControlPlaneAPIAuthConfiguration {
	if in == nil {
		return nil
	}
	out := new(KonnectControlPlaneAPIAuthConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtension) DeepCopyInto(out *KonnectExtension) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtension.
func (in *KonnectExtension) DeepCopy() *KonnectExtension {
	if in == nil {
		return nil
	}
	out := new(KonnectExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectExtension) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionList) DeepCopyInto(out *KonnectExtensionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectExtension, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionList.
func (in *KonnectExtensionList) DeepCopy() *KonnectExtensionList {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectExtensionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionSpec) DeepCopyInto(out *KonnectExtensionSpec) {
	*out = *in
	in.ControlPlaneRef.DeepCopyInto(&out.ControlPlaneRef)
	out.AuthConfiguration = in.AuthConfiguration
	if in.ClusterDataPlaneLabels != nil {
		in, out := &in.ClusterDataPlaneLabels, &out.ClusterDataPlaneLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionSpec.
func (in *KonnectExtensionSpec) DeepCopy() *KonnectExtensionSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionStatus) DeepCopyInto(out *KonnectExtensionStatus) {
	*out = *in
	if in.DataPlaneRefs != nil {
		in, out := &in.DataPlaneRefs, &out.DataPlaneRefs
		*out = make([]commonv1alpha1.NamespacedRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionStatus.
func (in *KonnectExtensionStatus) DeepCopy() *KonnectExtensionStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LLMPrompt) DeepCopyInto(out *LLMPrompt) {
	*out = *in
	if in.Role != nil {
		in, out := &in.Role, &out.Role
		*out = new(LLMPromptRole)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LLMPrompt.
func (in *LLMPrompt) DeepCopy() *LLMPrompt {
	if in == nil {
		return nil
	}
	out := new(LLMPrompt)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LLMPromptParams) DeepCopyInto(out *LLMPromptParams) {
	*out = *in
	if in.Temperature != nil {
		in, out := &in.Temperature, &out.Temperature
		*out = new(string)
		**out = **in
	}
	if in.MaxTokens != nil {
		in, out := &in.MaxTokens, &out.MaxTokens
		*out = new(int)
		**out = **in
	}
	if in.TopK != nil {
		in, out := &in.TopK, &out.TopK
		*out = new(int)
		**out = **in
	}
	if in.TopP != nil {
		in, out := &in.TopP, &out.TopP
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LLMPromptParams.
func (in *LLMPromptParams) DeepCopy() *LLMPromptParams {
	if in == nil {
		return nil
	}
	out := new(LLMPromptParams)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LargeLanguageModels) DeepCopyInto(out *LargeLanguageModels) {
	*out = *in
	if in.CloudHosted != nil {
		in, out := &in.CloudHosted, &out.CloudHosted
		*out = make([]CloudHostedLargeLanguageModel, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LargeLanguageModels.
func (in *LargeLanguageModels) DeepCopy() *LargeLanguageModels {
	if in == nil {
		return nil
	}
	out := new(LargeLanguageModels)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MetricsConfig) DeepCopyInto(out *MetricsConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MetricsConfig.
func (in *MetricsConfig) DeepCopy() *MetricsConfig {
	if in == nil {
		return nil
	}
	out := new(MetricsConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServiceSelector) DeepCopyInto(out *ServiceSelector) {
	*out = *in
	if in.MatchNames != nil {
		in, out := &in.MatchNames, &out.MatchNames
		*out = make([]ServiceSelectorEntry, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceSelector.
func (in *ServiceSelector) DeepCopy() *ServiceSelector {
	if in == nil {
		return nil
	}
	out := new(ServiceSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServiceSelectorEntry) DeepCopyInto(out *ServiceSelectorEntry) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceSelectorEntry.
func (in *ServiceSelectorEntry) DeepCopy() *ServiceSelectorEntry {
	if in == nil {
		return nil
	}
	out := new(ServiceSelectorEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WatchNamespaceGrant) DeepCopyInto(out *WatchNamespaceGrant) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WatchNamespaceGrant.
func (in *WatchNamespaceGrant) DeepCopy() *WatchNamespaceGrant {
	if in == nil {
		return nil
	}
	out := new(WatchNamespaceGrant)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *WatchNamespaceGrant) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WatchNamespaceGrantFrom) DeepCopyInto(out *WatchNamespaceGrantFrom) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WatchNamespaceGrantFrom.
func (in *WatchNamespaceGrantFrom) DeepCopy() *WatchNamespaceGrantFrom {
	if in == nil {
		return nil
	}
	out := new(WatchNamespaceGrantFrom)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WatchNamespaceGrantList) DeepCopyInto(out *WatchNamespaceGrantList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]WatchNamespaceGrant, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WatchNamespaceGrantList.
func (in *WatchNamespaceGrantList) DeepCopy() *WatchNamespaceGrantList {
	if in == nil {
		return nil
	}
	out := new(WatchNamespaceGrantList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *WatchNamespaceGrantList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WatchNamespaceGrantSpec) DeepCopyInto(out *WatchNamespaceGrantSpec) {
	*out = *in
	if in.From != nil {
		in, out := &in.From, &out.From
		*out = make([]WatchNamespaceGrantFrom, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WatchNamespaceGrantSpec.
func (in *WatchNamespaceGrantSpec) DeepCopy() *WatchNamespaceGrantSpec {
	if in == nil {
		return nil
	}
	out := new(WatchNamespaceGrantSpec)
	in.DeepCopyInto(out)
	return out
}
