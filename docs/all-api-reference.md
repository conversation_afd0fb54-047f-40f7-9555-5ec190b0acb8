<!-- This document is generated by kong/kubernetes-configuration's 'generate.docs' make target, DO NOT EDIT -->

## Packages
- [configuration.konghq.com/v1](#configurationkonghqcomv1)
- [configuration.konghq.com/v1alpha1](#configurationkonghqcomv1alpha1)
- [configuration.konghq.com/v1beta1](#configurationkonghqcomv1beta1)
- [gateway-operator.konghq.com/v1alpha1](#gateway-operatorkonghqcomv1alpha1)
- [gateway-operator.konghq.com/v1beta1](#gateway-operatorkonghqcomv1beta1)
- [gateway-operator.konghq.com/v2alpha1](#gateway-operatorkonghqcomv2alpha1)
- [incubator.ingress-controller.konghq.com/v1alpha1](#incubatoringress-controllerkonghqcomv1alpha1)
- [konnect.konghq.com/v1alpha1](#konnectkonghqcomv1alpha1)
- [konnect.konghq.com/v1alpha2](#konnectkonghqcomv1alpha2)


## configuration.konghq.com/v1

Package v1 contains API Schema definitions for the konghq.com v1 API group.

- [KongClusterPlugin](#kongclusterplugin)
- [KongConsumer](#kongconsumer)
- [KongIngress](#kongingress)
- [KongPlugin](#kongplugin)
### KongClusterPlugin


KongClusterPlugin is the Schema for the kongclusterplugins API.

<!-- kong_cluster_plugin description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1`
| `kind` _string_ | `KongClusterPlugin`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `consumerRef` _string_ | ConsumerRef is a reference to a particular consumer. |
| `disabled` _boolean_ | Disabled set if the plugin is disabled or not. |
| `config` _[JSON](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#json-v1-apiextensions-k8s-io)_ | Config contains the plugin configuration. It's a list of keys and values required to configure the plugin. Please read the documentation of the plugin being configured to set values in here. For any plugin in Kong, anything that goes in the `config` JSON key in the Admin API request, goes into this property. Only one of `config` or `configFrom` may be used in a KongClusterPlugin, not both at once. |
| `configFrom` _[NamespacedConfigSource](#namespacedconfigsource)_ | ConfigFrom references a secret containing the plugin configuration. This should be used when the plugin configuration contains sensitive information, such as AWS credentials in the Lambda plugin or the client secret in the OIDC plugin. Only one of `config` or `configFrom` may be used in a KongClusterPlugin, not both at once. |
| `configPatches` _[NamespacedConfigPatch](#namespacedconfigpatch) array_ | ConfigPatches represents JSON patches to the configuration of the plugin. Each item means a JSON patch to add something in the configuration, where path is specified in `path` and value is in `valueFrom` referencing a key in a secret. When Config is specified, patches will be applied to the configuration in Config. Otherwise, patches will be applied to an empty object. |
| `plugin` _string_ | PluginName is the name of the plugin to which to apply the config. |
| `run_on` _string_ | RunOn configures the plugin to run on the first or the second or both nodes in case of a service mesh deployment. |
| `protocols` _[KongProtocol](#kongprotocol) array_ | Protocols configures plugin to run on requests received on specific protocols. |
| `ordering` _[PluginOrdering](#pluginordering)_ | Ordering overrides the normal plugin execution order. It's only available on Kong Enterprise. `<phase>` is a request processing phase (for example, `access` or `body_filter`) and `<plugin>` is the name of the plugin that will run before or after the KongPlugin. For example, a KongPlugin with `plugin: rate-limiting` and `before.access: ["key-auth"]` will create a rate limiting plugin that limits requests _before_ they are authenticated. |
| `instance_name` _string_ | InstanceName is an optional custom name to identify an instance of the plugin. This is useful when running the same plugin in multiple contexts, for example, on multiple services. |



### KongConsumer


KongConsumer is the Schema for the kongconsumers API.

<!-- kong_consumer description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1`
| `kind` _string_ | `KongConsumer`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `username` _string_ | Username is a Kong cluster-unique username of the consumer. |
| `custom_id` _string_ | CustomID is a Kong cluster-unique existing ID for the consumer - useful for mapping Kong with users in your existing database. |
| `credentials` _string array_ | Credentials are references to secrets containing a credential to be provisioned in Kong. |
| `consumerGroups` _string array_ | ConsumerGroups are references to consumer groups (that consumer wants to be part of) provisioned in Kong. |
| `spec` _[KongConsumerSpec](#kongconsumerspec)_ |  |



### KongIngress


KongIngress is the Schema for the kongingresses API.
Deprecated: Use Gateway API instead.

<!-- kong_ingress description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1`
| `kind` _string_ | `KongIngress`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `upstream` _[KongIngressUpstream](#kongingressupstream)_ | Upstream represents a virtual hostname and can be used to loadbalance incoming requests over multiple targets (e.g. Kubernetes `Services` can be a target, OR `Endpoints` can be targets). |
| `proxy` _[KongIngressService](#kongingressservice)_ | Proxy defines additional connection options for the routes to be configured in the Kong Gateway, e.g. `connection_timeout`, `retries`, etc. |
| `route` _[KongIngressRoute](#kongingressroute)_ | Route define rules to match client requests. Each Route is associated with a Service, and a Service may have multiple Routes associated to it. |



### KongPlugin


KongPlugin is the Schema for the kongplugins API.

<!-- kong_plugin description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1`
| `kind` _string_ | `KongPlugin`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `consumerRef` _string_ | ConsumerRef is a reference to a particular consumer. |
| `disabled` _boolean_ | Disabled set if the plugin is disabled or not. |
| `config` _[JSON](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#json-v1-apiextensions-k8s-io)_ | Config contains the plugin configuration. It's a list of keys and values required to configure the plugin. Please read the documentation of the plugin being configured to set values in here. For any plugin in Kong, anything that goes in the `config` JSON key in the Admin API request, goes into this property. Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once. |
| `configFrom` _[ConfigSource](#configsource)_ | ConfigFrom references a secret containing the plugin configuration. This should be used when the plugin configuration contains sensitive information, such as AWS credentials in the Lambda plugin or the client secret in the OIDC plugin. Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once. |
| `configPatches` _[ConfigPatch](#configpatch) array_ | ConfigPatches represents JSON patches to the configuration of the plugin. Each item means a JSON patch to add something in the configuration, where path is specified in `path` and value is in `valueFrom` referencing a key in a secret. When Config is specified, patches will be applied to the configuration in Config. Otherwise, patches will be applied to an empty object. |
| `plugin` _string_ | PluginName is the name of the plugin to which to apply the config. |
| `run_on` _string_ | RunOn configures the plugin to run on the first or the second or both nodes in case of a service mesh deployment. |
| `protocols` _[KongProtocol](#kongprotocol) array_ | Protocols configures plugin to run on requests received on specific protocols. |
| `ordering` _[PluginOrdering](#pluginordering)_ | Ordering overrides the normal plugin execution order. It's only available on Kong Enterprise. `<phase>` is a request processing phase (for example, `access` or `body_filter`) and `<plugin>` is the name of the plugin that will run before or after the KongPlugin. For example, a KongPlugin with `plugin: rate-limiting` and `before.access: ["key-auth"]` will create a rate limiting plugin that limits requests _before_ they are authenticated. |
| `instance_name` _string_ | InstanceName is an optional custom name to identify an instance of the plugin. This is useful when running the same plugin in multiple contexts, for example, on multiple services. |



### Types

In this section you will find types that the CRDs rely on.




#### ConfigPatch


ConfigPatch is a JSON patch (RFC6902) to add values from Secret to the generated configuration.
It is an equivalent of the following patch:
`{"op": "add", "path": {.Path}, "value": {.ComputedValueFrom}}`.



| Field | Description |
| --- | --- |
| `path` _string_ | Path is the JSON-Pointer value (RFC6901) that references a location within the target configuration. |
| `valueFrom` _[ConfigSource](#configsource)_ | ValueFrom is the reference to a key of a secret where the patched value comes from. |


_Appears in:_
- [KongPlugin](#kongplugin)

#### ConfigSource


ConfigSource is a wrapper around SecretValueFromSource.



| Field | Description |
| --- | --- |
| `secretKeyRef` _[SecretValueFromSource](#secretvaluefromsource)_ | Specifies a name and a key of a secret to refer to. The namespace is implicitly set to the one of referring object. |


_Appears in:_
- [ConfigPatch](#configpatch)
- [KongPlugin](#kongplugin)



#### KongConsumerSpec


KongConsumerSpec defines the specification of the KongConsumer.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this Consumer is associated with. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the consumer. |


_Appears in:_
- [KongConsumer](#kongconsumer)



#### KongIngressRoute


KongIngressRoute contains KongIngress route configuration.
It contains the subset of `go-kong.kong.Route` fields supported by `kongstate.Route.overrideByKongIngress`.
Deprecated: use Ingress' annotations instead.



| Field | Description |
| --- | --- |
| `methods` _string array_ | Methods is a list of HTTP methods that match this Route. Deprecated: use Ingress' "konghq.com/methods" annotation instead. |
| `headers` _object (keys:string, values:string array)_ | Headers contains one or more lists of values indexed by header name that will cause this Route to match if present in the request. The Host header cannot be used with this attribute. Deprecated: use Ingress' "konghq.com/headers" annotation instead. |
| `protocols` _[KongProtocol](#kongprotocol) array_ | Protocols is an array of the protocols this Route should allow. Deprecated: use Ingress' "konghq.com/protocols" annotation instead. |
| `regex_priority` _integer_ | RegexPriority is a number used to choose which route resolves a given request when several routes match it using regexes simultaneously. Deprecated: use Ingress' "konghq.com/regex-priority" annotation instead. |
| `strip_path` _boolean_ | StripPath sets When matching a Route via one of the paths strip the matching prefix from the upstream request URL. Deprecated: use Ingress' "konghq.com/strip-path" annotation instead. |
| `preserve_host` _boolean_ | PreserveHost sets When matching a Route via one of the hosts domain names, use the request Host header in the upstream request headers. If set to false, the upstream Host header will be that of the Service’s host. Deprecated: use Ingress' "konghq.com/preserve-host" annotation instead. |
| `https_redirect_status_code` _integer_ | HTTPSRedirectStatusCode is the status code Kong responds with when all properties of a Route match except the protocol. Deprecated: use Ingress' "ingress.kubernetes.io/force-ssl-redirect" or "konghq.com/https-redirect-status-code" annotations instead. |
| `path_handling` _string_ | PathHandling controls how the Service path, Route path and requested path are combined when sending a request to the upstream. Deprecated: use Ingress' "konghq.com/path-handling" annotation instead. |
| `snis` _string array_ | SNIs is a list of SNIs that match this Route when using stream routing. Deprecated: use Ingress' "konghq.com/snis" annotation instead. |
| `request_buffering` _boolean_ | RequestBuffering sets whether to enable request body buffering or not. Deprecated: use Ingress' "konghq.com/request-buffering" annotation instead. |
| `response_buffering` _boolean_ | ResponseBuffering sets whether to enable response body buffering or not. Deprecated: use Ingress' "konghq.com/response-buffering" annotation instead. |


_Appears in:_
- [KongIngress](#kongingress)

#### KongIngressService


KongIngressService contains KongIngress service configuration.
It contains the subset of go-kong.kong.Service fields supported by kongstate.Service.overrideByKongIngress.
Deprecated: use Service's annotations instead.



| Field | Description |
| --- | --- |
| `protocol` _string_ | The protocol used to communicate with the upstream. Deprecated: use Service's "konghq.com/protocol" annotation instead. |
| `path` _string_ | (optional) The path to be used in requests to the upstream server. Deprecated: use Service's "konghq.com/path" annotation instead. |
| `retries` _integer_ | The number of retries to execute upon failure to proxy. Deprecated: use Service's "konghq.com/retries" annotation instead. |
| `connect_timeout` _integer_ | The timeout in milliseconds for	establishing a connection to the upstream server. Deprecated: use Service's "konghq.com/connect-timeout" annotation instead. |
| `read_timeout` _integer_ | The timeout in milliseconds between two successive read operations for transmitting a request to the upstream server. Deprecated: use Service's "konghq.com/read-timeout" annotation instead. |
| `write_timeout` _integer_ | The timeout in milliseconds between two successive write operations for transmitting a request to the upstream server. Deprecated: use Service's "konghq.com/write-timeout" annotation instead. |


_Appears in:_
- [KongIngress](#kongingress)

#### KongIngressUpstream


KongIngressUpstream contains KongIngress upstream configuration.
It contains the subset of `go-kong.kong.Upstream` fields supported by `kongstate.Upstream.overrideByKongIngress`.



| Field | Description |
| --- | --- |
| `host_header` _string_ | HostHeader is The hostname to be used as Host header when proxying requests through Kong. |
| `algorithm` _string_ | Algorithm is the load balancing algorithm to use. Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency". |
| `slots` _integer_ | Slots is the number of slots in the load balancer algorithm. |
| `healthchecks` _[Healthcheck](#healthcheck)_ | Healthchecks defines the health check configurations in Kong. |
| `hash_on` _string_ | HashOn defines what to use as hashing input. Accepted values are: "none", "consumer", "ip", "header", "cookie", "path", "query_arg", "uri_capture". |
| `hash_fallback` _string_ | HashFallback defines What to use as hashing input if the primary hash_on does not return a hash. Accepted values are: "none", "consumer", "ip", "header", "cookie". |
| `hash_on_header` _string_ | HashOnHeader defines the header name to take the value from as hash input. Only required when "hash_on" is set to "header". |
| `hash_fallback_header` _string_ | HashFallbackHeader is the header name to take the value from as hash input. Only required when "hash_fallback" is set to "header". |
| `hash_on_cookie` _string_ | The cookie name to take the value from as hash input. Only required when "hash_on" or "hash_fallback" is set to "cookie". |
| `hash_on_cookie_path` _string_ | The cookie path to set in the response headers. Only required when "hash_on" or "hash_fallback" is set to "cookie". |
| `hash_on_query_arg` _string_ | HashOnQueryArg is the query string parameter whose value is the hash input when "hash_on" is set to "query_arg". |
| `hash_fallback_query_arg` _string_ | HashFallbackQueryArg is the "hash_fallback" version of HashOnQueryArg. |
| `hash_on_uri_capture` _string_ | HashOnURICapture is the name of the capture group whose value is the hash input when "hash_on" is set to "uri_capture". |
| `hash_fallback_uri_capture` _string_ | HashFallbackURICapture is the "hash_fallback" version of HashOnURICapture. |


_Appears in:_
- [KongIngress](#kongingress)



#### KongProtocol
_Underlying type:_ `string`

KongProtocol is a valid Kong protocol.
This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342





_Appears in:_
- [KongClusterPlugin](#kongclusterplugin)
- [KongIngressRoute](#kongingressroute)
- [KongPlugin](#kongplugin)

#### NamespacedConfigPatch


NamespacedConfigPatch is a JSON patch to add values from secrets to KongClusterPlugin
to the generated configuration of plugin in Kong.



| Field | Description |
| --- | --- |
| `path` _string_ | Path is the JSON path to add the patch. |
| `valueFrom` _[NamespacedConfigSource](#namespacedconfigsource)_ | ValueFrom is the reference to a key of a secret where the patched value comes from. |


_Appears in:_
- [KongClusterPlugin](#kongclusterplugin)

#### NamespacedConfigSource


NamespacedConfigSource is a wrapper around NamespacedSecretValueFromSource.



| Field | Description |
| --- | --- |
| `secretKeyRef` _[NamespacedSecretValueFromSource](#namespacedsecretvaluefromsource)_ | Specifies a name, a namespace, and a key of a secret to refer to. |


_Appears in:_
- [KongClusterPlugin](#kongclusterplugin)
- [NamespacedConfigPatch](#namespacedconfigpatch)

#### NamespacedSecretValueFromSource


NamespacedSecretValueFromSource represents the source of a secret value specifying the secret namespace.



| Field | Description |
| --- | --- |
| `namespace` _string_ | The namespace containing the secret. |
| `name` _string_ | The secret containing the key. |
| `key` _string_ | The key containing the value. |


_Appears in:_
- [NamespacedConfigSource](#namespacedconfigsource)

#### SecretValueFromSource


SecretValueFromSource represents the source of a secret value.



| Field | Description |
| --- | --- |
| `name` _string_ | The secret containing the key. |
| `key` _string_ | The key containing the value. |


_Appears in:_
- [ConfigSource](#configsource)


## configuration.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the configuration.konghq.com v1alpha1 API group.

- [IngressClassParameters](#ingressclassparameters)
- [KongCACertificate](#kongcacertificate)
- [KongCertificate](#kongcertificate)
- [KongCredentialACL](#kongcredentialacl)
- [KongCredentialAPIKey](#kongcredentialapikey)
- [KongCredentialBasicAuth](#kongcredentialbasicauth)
- [KongCredentialHMAC](#kongcredentialhmac)
- [KongCredentialJWT](#kongcredentialjwt)
- [KongCustomEntity](#kongcustomentity)
- [KongDataPlaneClientCertificate](#kongdataplaneclientcertificate)
- [KongKey](#kongkey)
- [KongKeySet](#kongkeyset)
- [KongLicense](#konglicense)
- [KongPluginBinding](#kongpluginbinding)
- [KongRoute](#kongroute)
- [KongSNI](#kongsni)
- [KongService](#kongservice)
- [KongTarget](#kongtarget)
- [KongUpstream](#kongupstream)
- [KongVault](#kongvault)
### IngressClassParameters


IngressClassParameters is the Schema for the IngressClassParameters API.

<!-- ingress_class_parameters description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `IngressClassParameters`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[IngressClassParametersSpec](#ingressclassparametersspec)_ | Spec is the IngressClassParameters specification. |



### KongCACertificate


KongCACertificate is the schema for CACertificate API which defines a Kong CA Certificate.

<!-- kong_ca_certificate description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCACertificate`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCACertificateSpec](#kongcacertificatespec)_ |  |



### KongCertificate


KongCertificate is the schema for Certificate API which defines a Kong Certificate.

<!-- kong_certificate description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCertificate`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCertificateSpec](#kongcertificatespec)_ |  |



### KongCredentialACL


KongCredentialACL is the schema for ACL credentials API which defines a ACL credential for consumers.

<!-- kong_credential_acl description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCredentialACL`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCredentialACLSpec](#kongcredentialaclspec)_ | Spec contains the ACL credential specification. |



### KongCredentialAPIKey


KongCredentialAPIKey is the schema for API key credentials API which defines a API key credential for consumers.

<!-- kong_credential_api_key description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCredentialAPIKey`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCredentialAPIKeySpec](#kongcredentialapikeyspec)_ | Spec contains the API Key credential specification. |



### KongCredentialBasicAuth


KongCredentialBasicAuth is the schema for BasicAuth credentials API which defines a BasicAuth credential for consumers.

<!-- kong_credential_basic_auth description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCredentialBasicAuth`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCredentialBasicAuthSpec](#kongcredentialbasicauthspec)_ | Spec contains the BasicAuth credential specification. |



### KongCredentialHMAC


KongCredentialHMAC is the schema for HMAC credentials API which defines a HMAC credential for consumers.

<!-- kong_credential_hmac description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCredentialHMAC`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCredentialHMACSpec](#kongcredentialhmacspec)_ | Spec contains the HMAC credential specification. |



### KongCredentialJWT


KongCredentialJWT is the schema for JWT credentials API which defines a JWT credential for consumers.

<!-- kong_credential_jwt description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCredentialJWT`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCredentialJWTSpec](#kongcredentialjwtspec)_ | Spec contains the JWT credential specification. |



### KongCustomEntity


KongCustomEntity defines a "custom" Kong entity that KIC cannot support the entity type directly.

<!-- kong_custom_entity description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongCustomEntity`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongCustomEntitySpec](#kongcustomentityspec)_ |  |



### KongDataPlaneClientCertificate


KongDataPlaneClientCertificate is the schema for KongDataPlaneClientCertificate API which defines a KongDataPlaneClientCertificate entity.

<!-- kong_data_plane_client_certificate description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongDataPlaneClientCertificate`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongDataPlaneClientCertificateSpec](#kongdataplaneclientcertificatespec)_ |  |



### KongKey


KongKey is the schema for KongKey API which defines a KongKey entity.

<!-- kong_key description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongKey`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongKeySpec](#kongkeyspec)_ |  |



### KongKeySet


KongKeySet is the schema for KongKeySet API which defines a KongKeySet entity.

<!-- kong_key_set description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongKeySet`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongKeySetSpec](#kongkeysetspec)_ |  |



### KongLicense


KongLicense stores a Kong enterprise license to apply to managed Kong gateway instances.

<!-- kong_license description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongLicense`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `rawLicenseString` _string_ | RawLicenseString is a string with the raw content of the license. |
| `enabled` _boolean_ | Enabled is set to true to let controllers (like KIC or KGO) to reconcile it. Default value is true to apply the license by default. |



### KongPluginBinding


KongPluginBinding is the schema for Plugin Bindings API which defines a Kong Plugin Binding.

<!-- kong_plugin_binding description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongPluginBinding`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongPluginBindingSpec](#kongpluginbindingspec)_ |  |



### KongRoute


KongRoute is the schema for Routes API which defines a Kong Route.

<!-- kong_route description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongRoute`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongRouteSpec](#kongroutespec)_ |  |



### KongSNI


KongSNI is the schema for SNI API which defines a Kong SNI.

<!-- kong_sni description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongSNI`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongSNISpec](#kongsnispec)_ |  |



### KongService


KongService is the schema for Services API which defines a Kong Service.

<!-- kong_service description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongService`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongServiceSpec](#kongservicespec)_ |  |



### KongTarget


KongTarget is the schema for Target API which defines a Kong Target attached to a Kong Upstream.

<!-- kong_target description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongTarget`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongTargetSpec](#kongtargetspec)_ |  |



### KongUpstream


KongUpstream is the schema for Upstream API which defines a Kong Upstream.

<!-- kong_upstream description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongUpstream`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongUpstreamSpec](#kongupstreamspec)_ |  |



### KongVault


KongVault is the schema for kongvaults API which defines a custom Kong vault.
A Kong vault is a storage to store sensitive data, where the values can be referenced in configuration of plugins.
See: https://docs.konghq.com/gateway/latest/kong-enterprise/secrets-management/

<!-- kong_vault description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1alpha1`
| `kind` _string_ | `KongVault`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongVaultSpec](#kongvaultspec)_ |  |



### Types

In this section you will find types that the CRDs rely on.
#### ControlPlaneRef
_Underlying type:_ `[ControlPlaneRef](#controlplaneref)`

ControlPlaneRef is the schema for the ControlPlaneRef type.
It is used to reference a Control Plane entity.



| Field | Description |
| --- | --- |
| `type` _string_ | Type indicates the type of the control plane being referenced. Allowed values: - konnectID - konnectNamespacedRef - kic<br /><br /> The default is kic, which implies that the Control Plane is KIC. |
| `konnectID` _[KonnectIDType](#konnectidtype)_ | KonnectID is the schema for the KonnectID type. This field is required when the Type is konnectID. |
| `konnectNamespacedRef` _[KonnectNamespacedRef](#konnectnamespacedref)_ | KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster. It contains the name of the Konnect Control Plane. This field is required when the Type is konnectNamespacedRef. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### ControllerReference


ControllerReference is a reference to a controller that reconciles the KongLicense.



| Field | Description |
| --- | --- |
| `group` _[Group](#group)_ | Group is the group of referent. It should be empty if the referent is in "core" group (like pod). |
| `kind` _[Kind](#kind)_ | Kind is the kind of the referent. By default the nil kind means kind Pod. |
| `namespace` _[Namespace](#namespace)_ | Namespace is the namespace of the referent. It should be empty if the referent is cluster scoped. |
| `name` _[ObjectName](#objectname)_ | Name is the name of the referent. |


_Appears in:_
- [KongLicenseControllerStatus](#konglicensecontrollerstatus)

#### Group
_Underlying type:_ `string`

Group refers to a Kubernetes Group. It must either be an empty string or a
RFC 1123 subdomain.





_Appears in:_
- [ControllerReference](#controllerreference)

#### IngressClassParametersSpec


IngressClassParametersSpec defines the desired state of IngressClassParameters.



| Field | Description |
| --- | --- |
| `serviceUpstream` _boolean_ | Offload load-balancing to kube-proxy or sidecar. |
| `enableLegacyRegexDetection` _boolean_ | EnableLegacyRegexDetection automatically detects if ImplementationSpecific Ingress paths are regular expression paths using the legacy 2.x heuristic. The controller adds the "~" prefix to those paths if the Kong version is 3.0 or higher. |


_Appears in:_
- [IngressClassParameters](#ingressclassparameters)

#### KeySetRef


KeySetRef is the schema for the KeySetRef type.
It is used to reference a KeySet entity.



| Field | Description |
| --- | --- |
| `type` _[KeySetRefType](#keysetreftype)_ | Type defines type of the KeySet object reference. It can be one of: - konnectID - namespacedRef |
| `konnectID` _string_ | KonnectID is the schema for the KonnectID type. This field is required when the Type is konnectID. |
| `namespacedRef` _[NameRef](#nameref)_ | NamespacedRef is a reference to a KeySet entity inside the cluster. This field is required when the Type is namespacedRef. |


_Appears in:_
- [KongKeySpec](#kongkeyspec)

#### KeySetRefType
_Underlying type:_ `string`

KeySetRefType is the enum type for the KeySetRef.





_Appears in:_
- [KeySetRef](#keysetref)

#### Kind
_Underlying type:_ `string`

Kind refers to a Kubernetes kind.





_Appears in:_
- [ControllerReference](#controllerreference)

#### KongCACertificateAPISpec


KongCACertificateAPISpec contains the API specification for the KongCACertificate.



| Field | Description |
| --- | --- |
| `cert` _string_ | Cert is the PEM-encoded CA certificate. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the certificate. |


_Appears in:_
- [KongCACertificateSpec](#kongcacertificatespec)

#### KongCACertificateSpec


KongCACertificateSpec contains the specification for the KongCACertificate.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef references the Konnect Control Plane that this KongCACertificate should be created in. |
| `cert` _string_ | Cert is the PEM-encoded CA certificate. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the certificate. |


_Appears in:_
- [KongCACertificate](#kongcacertificate)



#### KongCertificateAPISpec


KongCertificateAPISpec contains the API specification for the KongCertificate.



| Field | Description |
| --- | --- |
| `cert` _string_ | Cert is the PEM-encoded certificate. |
| `cert_alt` _string_ | CertAlt is the PEM-encoded certificate. This should only be set if you have both RSA and ECDSA types of certificate available and would like Kong to prefer serving using ECDSA certs when client advertises support for it. |
| `key` _string_ | Key is the PEM-encoded private key. |
| `key_alt` _string_ | KeyAlt is the PEM-encoded private key. This should only be set if you have both RSA and ECDSA types of certificate available and would like Kong to prefer serving using ECDSA certs when client advertises support for it. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the certificate. |


_Appears in:_
- [KongCertificateSpec](#kongcertificatespec)

#### KongCertificateSpec


KongCertificateSpec contains the specification for the KongCertificate.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef references the Konnect Control Plane that this KongCertificate should be created in. |
| `cert` _string_ | Cert is the PEM-encoded certificate. |
| `cert_alt` _string_ | CertAlt is the PEM-encoded certificate. This should only be set if you have both RSA and ECDSA types of certificate available and would like Kong to prefer serving using ECDSA certs when client advertises support for it. |
| `key` _string_ | Key is the PEM-encoded private key. |
| `key_alt` _string_ | KeyAlt is the PEM-encoded private key. This should only be set if you have both RSA and ECDSA types of certificate available and would like Kong to prefer serving using ECDSA certs when client advertises support for it. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the certificate. |


_Appears in:_
- [KongCertificate](#kongcertificate)



#### KongCredentialACLAPISpec


KongCredentialACLAPISpec defines specification of an ACL credential.



| Field | Description |
| --- | --- |
| `group` _string_ | Group is the name for the ACL credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the ACL credential. |


_Appears in:_
- [KongCredentialACLSpec](#kongcredentialaclspec)

#### KongCredentialACLSpec


KongCredentialACLSpec defines specification of Kong ACL.



| Field | Description |
| --- | --- |
| `consumerRef` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core)_ | ConsumerRef is a reference to a Consumer this KongCredentialACL is associated with. |
| `group` _string_ | Group is the name for the ACL credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the ACL credential. |


_Appears in:_
- [KongCredentialACL](#kongcredentialacl)



#### KongCredentialAPIKeyAPISpec


KongCredentialAPIKeyAPISpec defines specification of an API Key credential.



| Field | Description |
| --- | --- |
| `key` _string_ | Key is the key for the API Key credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the API Key credential. |


_Appears in:_
- [KongCredentialAPIKeySpec](#kongcredentialapikeyspec)

#### KongCredentialAPIKeySpec


KongCredentialAPIKeySpec defines specification of a Kong Route.



| Field | Description |
| --- | --- |
| `consumerRef` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core)_ | ConsumerRef is a reference to a Consumer this KongCredentialAPIKey is associated with. |
| `key` _string_ | Key is the key for the API Key credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the API Key credential. |


_Appears in:_
- [KongCredentialAPIKey](#kongcredentialapikey)



#### KongCredentialBasicAuthAPISpec


KongCredentialBasicAuthAPISpec defines specification of a BasicAuth credential.



| Field | Description |
| --- | --- |
| `password` _string_ | Password is the password for the BasicAuth credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the BasicAuth credential. |
| `username` _string_ | Username is the username for the BasicAuth credential. |


_Appears in:_
- [KongCredentialBasicAuthSpec](#kongcredentialbasicauthspec)

#### KongCredentialBasicAuthSpec


KongCredentialBasicAuthSpec defines specification of a Kong Route.



| Field | Description |
| --- | --- |
| `consumerRef` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core)_ | ConsumerRef is a reference to a Consumer this CredentialBasicAuth is associated with. |
| `password` _string_ | Password is the password for the BasicAuth credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the BasicAuth credential. |
| `username` _string_ | Username is the username for the BasicAuth credential. |


_Appears in:_
- [KongCredentialBasicAuth](#kongcredentialbasicauth)



#### KongCredentialHMACAPISpec


KongCredentialHMACAPISpec defines specification of an HMAC credential.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier for the HMAC credential. |
| `secret` _string_ | Secret is the secret for the HMAC credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the HMAC credential. |
| `username` _string_ | Username is the username for the HMAC credential. |


_Appears in:_
- [KongCredentialHMACSpec](#kongcredentialhmacspec)

#### KongCredentialHMACSpec


KongCredentialHMACSpec defines specification of a Kong Route.



| Field | Description |
| --- | --- |
| `consumerRef` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core)_ | ConsumerRef is a reference to a Consumer this KongCredentialHMAC is associated with. |
| `id` _string_ | ID is the unique identifier for the HMAC credential. |
| `secret` _string_ | Secret is the secret for the HMAC credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the HMAC credential. |
| `username` _string_ | Username is the username for the HMAC credential. |


_Appears in:_
- [KongCredentialHMAC](#kongcredentialhmac)



#### KongCredentialJWTAPISpec


KongCredentialJWTAPISpec defines specification of an JWT credential.



| Field | Description |
| --- | --- |
| `algorithm` _string_ | Algorithm is the algorithm used to sign the JWT token. |
| `id` _string_ | ID is the unique identifier for the JWT credential. |
| `key` _string_ | Key is the key for the JWT credential. |
| `rsa_public_key` _string_ | RSA PublicKey is the RSA public key for the JWT credential. |
| `secret` _string_ | Secret is the secret for the JWT credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the JWT credential. |


_Appears in:_
- [KongCredentialJWTSpec](#kongcredentialjwtspec)

#### KongCredentialJWTSpec


KongCredentialJWTSpec defines specification of a Kong Route.



| Field | Description |
| --- | --- |
| `consumerRef` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core)_ | ConsumerRef is a reference to a Consumer this KongCredentialJWT is associated with. |
| `algorithm` _string_ | Algorithm is the algorithm used to sign the JWT token. |
| `id` _string_ | ID is the unique identifier for the JWT credential. |
| `key` _string_ | Key is the key for the JWT credential. |
| `rsa_public_key` _string_ | RSA PublicKey is the RSA public key for the JWT credential. |
| `secret` _string_ | Secret is the secret for the JWT credential. |
| `tags` _[Tags](#tags)_ | Tags is a list of tags for the JWT credential. |


_Appears in:_
- [KongCredentialJWT](#kongcredentialjwt)



#### KongCustomEntitySpec


KongCustomEntitySpec defines the specification of the KongCustomEntity.



| Field | Description |
| --- | --- |
| `type` _string_ | EntityType is the type of the Kong entity. The type is used in generating declarative configuration. |
| `fields` _[JSON](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#json-v1-apiextensions-k8s-io)_ | Fields defines the fields of the Kong entity itself. |
| `controllerName` _string_ | ControllerName specifies the controller that should reconcile it, like ingress class. |
| `parentRef` _[ObjectReference](#objectreference)_ | ParentRef references the kubernetes resource it attached to when its scope is "attached". Currently only KongPlugin/KongClusterPlugin allowed. This will make the custom entity to be attached to the entity(service/route/consumer) where the plugin is attached. |


_Appears in:_
- [KongCustomEntity](#kongcustomentity)



#### KongDataPlaneClientCertificateAPISpec


KongDataPlaneClientCertificateAPISpec defines the attributes of a Kong DP certificate.



| Field | Description |
| --- | --- |
| `cert` _string_ | Cert is the certificate in PEM format. Once the certificate gets programmed this field becomes immutable. |


_Appears in:_
- [KongDataPlaneClientCertificateSpec](#kongdataplaneclientcertificatespec)

#### KongDataPlaneClientCertificateSpec


KongDataPlaneClientCertificateSpec defines the spec for a KongDataPlaneClientCertificate.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a Konnect ControlPlane this KongDataPlaneClientCertificate is associated with. |
| `cert` _string_ | Cert is the certificate in PEM format. Once the certificate gets programmed this field becomes immutable. |


_Appears in:_
- [KongDataPlaneClientCertificate](#kongdataplaneclientcertificate)





#### KongKeyAPISpec


KongKeyAPISpec defines the attributes of a Kong Key.



| Field | Description |
| --- | --- |
| `kid` _string_ | KID is a unique identifier for a key. When JWK is provided, KID has to match the KID in the JWK. |
| `name` _string_ | Name is an optional name to associate with the given key. |
| `jwk` _string_ | JWK is a JSON Web Key represented as a string. The JWK must contain a KID field that matches the KID in the KongKey. Either JWK or PEM must be set. |
| `pem` _[PEMKeyPair](#pemkeypair)_ | PEM is a keypair in PEM format. Either JWK or PEM must be set. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the Key for grouping and filtering. |


_Appears in:_
- [KongKeySpec](#kongkeyspec)

#### KongKeySetAPISpec


KongKeySetAPISpec defines the attributes of a Kong KeySet.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is a name of the KeySet. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the KeySet for grouping and filtering. |


_Appears in:_
- [KongKeySetSpec](#kongkeysetspec)

#### KongKeySetSpec


KongKeySetSpec defines the spec for a KongKeySet.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a Konnect ControlPlane with which KongKeySet is associated. |
| `name` _string_ | Name is a name of the KeySet. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the KeySet for grouping and filtering. |


_Appears in:_
- [KongKeySet](#kongkeyset)



#### KongKeySpec


KongKeySpec defines the spec for a KongKey.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a Konnect ControlPlane this KongKey is associated with. |
| `keySetRef` _[KeySetRef](#keysetref)_ | KeySetRef is a reference to a KongKeySet this KongKey is attached to. ControlPlane referenced by a KongKeySet must be the same as the ControlPlane referenced by the KongKey. |
| `kid` _string_ | KID is a unique identifier for a key. When JWK is provided, KID has to match the KID in the JWK. |
| `name` _string_ | Name is an optional name to associate with the given key. |
| `jwk` _string_ | JWK is a JSON Web Key represented as a string. The JWK must contain a KID field that matches the KID in the KongKey. Either JWK or PEM must be set. |
| `pem` _[PEMKeyPair](#pemkeypair)_ | PEM is a keypair in PEM format. Either JWK or PEM must be set. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the Key for grouping and filtering. |


_Appears in:_
- [KongKey](#kongkey)



#### KongLicenseControllerStatus


KongLicenseControllerStatus is the status of owning KongLicense being processed
identified by the controllerName field.



| Field | Description |
| --- | --- |
| `controllerName` _string_ | ControllerName is an identifier of the controller to reconcile this KongLicense. Should be unique in the list of controller statuses. |
| `controllerRef` _[ControllerReference](#controllerreference)_ | ControllerRef is the reference of the controller to reconcile this KongLicense. It is usually the name of (KIC/KGO) pod that reconciles it. |
| `conditions` _[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#condition-v1-meta) array_ | Conditions describe the current conditions of the KongLicense on the controller. |


_Appears in:_
- [KongLicenseStatus](#konglicensestatus)



#### KongPluginBindingScope
_Underlying type:_ `string`

KongPluginBindingScope defines the scope of the plugin binding.
Allowed values are:
- OnlyTargets
- GlobalInControlPlane





_Appears in:_
- [KongPluginBindingSpec](#kongpluginbindingspec)

#### KongPluginBindingSpec


KongPluginBindingSpec defines specification of a KongPluginBinding.



| Field | Description |
| --- | --- |
| `pluginRef` _[PluginRef](#pluginref)_ | PluginReference is a reference to the KongPlugin or KongClusterPlugin resource. |
| `targets` _[KongPluginBindingTargets](#kongpluginbindingtargets)_ | Targets contains the targets references. It is possible to set multiple combinations of references, as described in https://docs.konghq.com/gateway/latest/key-concepts/plugins/#precedence The complete set of allowed combinations and their order of precedence for plugins configured to multiple entities is:<br /><br /> 1. Consumer + route + service 2. Consumer group + service + route 3. Consumer + route 4. Consumer + service 5. Consumer group + route 6. Consumer group + service 7. Route + service 8. Consumer 9. Consumer group 10. Route 11. Service |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KongPluginBinding is associated with. |
| `scope` _[KongPluginBindingScope](#kongpluginbindingscope)_ | Scope defines the scope of the plugin binding. |


_Appears in:_
- [KongPluginBinding](#kongpluginbinding)



#### KongPluginBindingTargets


KongPluginBindingTargets contains the targets references.



| Field | Description |
| --- | --- |
| `routeRef` _[TargetRefWithGroupKind](#targetrefwithgroupkind)_ | RouteReference can be used to reference one of the following resouces: - networking.k8s.io/Ingress - gateway.networking.k8s.io/HTTPRoute - gateway.networking.k8s.io/GRPCRoute - configuration.konghq.com/KongRoute |
| `serviceRef` _[TargetRefWithGroupKind](#targetrefwithgroupkind)_ | ServiceReference can be used to reference one of the following resouces: - core/Service or /Service - configuration.konghq.com/KongService |
| `consumerRef` _[TargetRef](#targetref)_ | ConsumerReference is used to reference a configuration.konghq.com/Consumer resource. The group/kind is fixed, therefore the reference is performed only by name. |
| `consumerGroupRef` _[TargetRef](#targetref)_ | ConsumerGroupReference is used to reference a configuration.konghq.com/ConsumerGroup resource. The group/kind is fixed, therefore the reference is performed only by name. |


_Appears in:_
- [KongPluginBindingSpec](#kongpluginbindingspec)

#### KongRouteAPISpec


KongRouteAPISpec represents the configuration of a Route in Kong as defined by the Konnect API.<br /><br />
These fields are mostly copied from sdk-konnect-go but some modifications have been made
to make the code generation required for Kubernetes CRDs work.



| Field | Description |
| --- | --- |
| `destinations` _Destinations array_ | A list of IP destinations of incoming connections that match this Route when using stream routing. Each entry is an object with fields "ip" (optionally in CIDR range notation) and/or "port". |
| `headers` _object (keys:string, values:string array)_ | One or more lists of values indexed by header name that will cause this Route to match if present in the request. The `Host` header cannot be used with this attribute: hosts should be specified using the `hosts` attribute. When `headers` contains only one value and that value starts with the special prefix `~*`, the value is interpreted as a regular expression. |
| `hosts` _string array_ | A list of domain names that match this Route. Note that the hosts value is case sensitive. |
| `https_redirect_status_code` _[HTTPSRedirectStatusCode](#httpsredirectstatuscode)_ | The status code Kong responds with when all properties of a Route match except the protocol i.e. if the protocol of the request is `HTTP` instead of `HTTPS`. `Location` header is injected by Kong if the field is set to 301, 302, 307 or 308. Note: This config applies only if the Route is configured to only accept the `https` protocol. |
| `methods` _string array_ | A list of HTTP methods that match this Route. |
| `name` _string_ | The name of the Route. Route names must be unique, and they are case sensitive. For example, there can be two different Routes named "test" and "Test". |
| `path_handling` _[PathHandling](#pathhandling)_ | Controls how the Service path, Route path and requested path are combined when sending a request to the upstream. See above for a detailed description of each behavior. |
| `paths` _string array_ | A list of paths that match this Route. |
| `preserve_host` _boolean_ | When matching a Route via one of the `hosts` domain names, use the request `Host` header in the upstream request headers. If set to `false`, the upstream `Host` header will be that of the Service's `host`. |
| `protocols` _RouteJSONProtocols array_ | An array of the protocols this Route should allow. See KongRoute for a list of accepted protocols. When set to only `"https"`, HTTP requests are answered with an upgrade error. When set to only `"http"`, HTTPS requests are answered with an error. |
| `regex_priority` _integer_ | A number used to choose which route resolves a given request when several routes match it using regexes simultaneously. When two routes match the path and have the same `regex_priority`, the older one (lowest `created_at`) is used. Note that the priority for non-regex routes is different (longer non-regex routes are matched before shorter ones). |
| `request_buffering` _boolean_ | Whether to enable request body buffering or not. With HTTP 1.1, it may make sense to turn this off on services that receive data with chunked transfer encoding. |
| `response_buffering` _boolean_ | Whether to enable response body buffering or not. With HTTP 1.1, it may make sense to turn this off on services that send data with chunked transfer encoding. |
| `snis` _string array_ | A list of SNIs that match this Route when using stream routing. |
| `sources` _Sources array_ | A list of IP sources of incoming connections that match this Route when using stream routing. Each entry is an object with fields "ip" (optionally in CIDR range notation) and/or "port". |
| `strip_path` _boolean_ | When matching a Route via one of the `paths`, strip the matching prefix from the upstream request URL. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Route for grouping and filtering. |


_Appears in:_
- [KongRouteSpec](#kongroutespec)

#### KongRouteSpec


KongRouteSpec defines spec of a Kong Route.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KongRoute is associated with. Route can either specify a ControlPlaneRef and be 'serviceless' route or specify a ServiceRef and be associated with a Service. |
| `serviceRef` _[ServiceRef](#serviceref)_ | ServiceRef is a reference to a Service this KongRoute is associated with. Route can either specify a ControlPlaneRef and be 'serviceless' route or specify a ServiceRef and be associated with a Service. |
| `destinations` _Destinations array_ | A list of IP destinations of incoming connections that match this Route when using stream routing. Each entry is an object with fields "ip" (optionally in CIDR range notation) and/or "port". |
| `headers` _object (keys:string, values:string array)_ | One or more lists of values indexed by header name that will cause this Route to match if present in the request. The `Host` header cannot be used with this attribute: hosts should be specified using the `hosts` attribute. When `headers` contains only one value and that value starts with the special prefix `~*`, the value is interpreted as a regular expression. |
| `hosts` _string array_ | A list of domain names that match this Route. Note that the hosts value is case sensitive. |
| `https_redirect_status_code` _[HTTPSRedirectStatusCode](#httpsredirectstatuscode)_ | The status code Kong responds with when all properties of a Route match except the protocol i.e. if the protocol of the request is `HTTP` instead of `HTTPS`. `Location` header is injected by Kong if the field is set to 301, 302, 307 or 308. Note: This config applies only if the Route is configured to only accept the `https` protocol. |
| `methods` _string array_ | A list of HTTP methods that match this Route. |
| `name` _string_ | The name of the Route. Route names must be unique, and they are case sensitive. For example, there can be two different Routes named "test" and "Test". |
| `path_handling` _[PathHandling](#pathhandling)_ | Controls how the Service path, Route path and requested path are combined when sending a request to the upstream. See above for a detailed description of each behavior. |
| `paths` _string array_ | A list of paths that match this Route. |
| `preserve_host` _boolean_ | When matching a Route via one of the `hosts` domain names, use the request `Host` header in the upstream request headers. If set to `false`, the upstream `Host` header will be that of the Service's `host`. |
| `protocols` _RouteJSONProtocols array_ | An array of the protocols this Route should allow. See KongRoute for a list of accepted protocols. When set to only `"https"`, HTTP requests are answered with an upgrade error. When set to only `"http"`, HTTPS requests are answered with an error. |
| `regex_priority` _integer_ | A number used to choose which route resolves a given request when several routes match it using regexes simultaneously. When two routes match the path and have the same `regex_priority`, the older one (lowest `created_at`) is used. Note that the priority for non-regex routes is different (longer non-regex routes are matched before shorter ones). |
| `request_buffering` _boolean_ | Whether to enable request body buffering or not. With HTTP 1.1, it may make sense to turn this off on services that receive data with chunked transfer encoding. |
| `response_buffering` _boolean_ | Whether to enable response body buffering or not. With HTTP 1.1, it may make sense to turn this off on services that send data with chunked transfer encoding. |
| `snis` _string array_ | A list of SNIs that match this Route when using stream routing. |
| `sources` _Sources array_ | A list of IP sources of incoming connections that match this Route when using stream routing. Each entry is an object with fields "ip" (optionally in CIDR range notation) and/or "port". |
| `strip_path` _boolean_ | When matching a Route via one of the `paths`, strip the matching prefix from the upstream request URL. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Route for grouping and filtering. |


_Appears in:_
- [KongRoute](#kongroute)



#### KongSNIAPISpec


KongSNIAPISpec defines the spec of an SNI.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the SNI. Required and must be a host or wildcard host. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the SNI for grouping and filtering. |


_Appears in:_
- [KongSNISpec](#kongsnispec)

#### KongSNISpec


KongSNISpec defines specification of a Kong SNI.



| Field | Description |
| --- | --- |
| `certificateRef` _[NameRef](#nameref)_ | CertificateRef is the reference to the certificate to which the KongSNI is attached. |
| `name` _string_ | Name is the name of the SNI. Required and must be a host or wildcard host. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the SNI for grouping and filtering. |


_Appears in:_
- [KongSNI](#kongsni)



#### KongServiceAPISpec


KongServiceAPISpec defines the specification of a Kong Service.



| Field | Description |
| --- | --- |
| `url` _string_ | Helper field to set `protocol`, `host`, `port` and `path` using a URL. This field is write-only and is not returned in responses. |
| `connect_timeout` _integer_ | The timeout in milliseconds for establishing a connection to the upstream server. |
| `enabled` _boolean_ | Whether the Service is active. If set to `false`, the proxy behavior will be as if any routes attached to it do not exist (404). Default: `true`. |
| `host` _string_ | The host of the upstream server. Note that the host value is case sensitive. |
| `name` _string_ | The Service name. |
| `path` _string_ | The path to be used in requests to the upstream server. |
| `port` _integer_ | The upstream server port. |
| `protocol` _[Protocol](#protocol)_ | The protocol used to communicate with the upstream. |
| `read_timeout` _integer_ | The timeout in milliseconds between two successive read operations for transmitting a request to the upstream server. |
| `retries` _integer_ | The number of retries to execute upon failure to proxy. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Service for grouping and filtering. |
| `tls_verify` _boolean_ | Whether to enable verification of upstream server TLS certificate. If set to `null`, then the Nginx default is respected. |
| `tls_verify_depth` _integer_ | Maximum depth of chain while verifying Upstream server's TLS certificate. If set to `null`, then the Nginx default is respected. |
| `write_timeout` _integer_ | The timeout in milliseconds between two successive write operations for transmitting a request to the upstream server. |


_Appears in:_
- [KongServiceSpec](#kongservicespec)

#### KongServiceSpec


KongServiceSpec defines specification of a Kong Service.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KongService is associated with. |
| `url` _string_ | Helper field to set `protocol`, `host`, `port` and `path` using a URL. This field is write-only and is not returned in responses. |
| `connect_timeout` _integer_ | The timeout in milliseconds for establishing a connection to the upstream server. |
| `enabled` _boolean_ | Whether the Service is active. If set to `false`, the proxy behavior will be as if any routes attached to it do not exist (404). Default: `true`. |
| `host` _string_ | The host of the upstream server. Note that the host value is case sensitive. |
| `name` _string_ | The Service name. |
| `path` _string_ | The path to be used in requests to the upstream server. |
| `port` _integer_ | The upstream server port. |
| `protocol` _[Protocol](#protocol)_ | The protocol used to communicate with the upstream. |
| `read_timeout` _integer_ | The timeout in milliseconds between two successive read operations for transmitting a request to the upstream server. |
| `retries` _integer_ | The number of retries to execute upon failure to proxy. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Service for grouping and filtering. |
| `tls_verify` _boolean_ | Whether to enable verification of upstream server TLS certificate. If set to `null`, then the Nginx default is respected. |
| `tls_verify_depth` _integer_ | Maximum depth of chain while verifying Upstream server's TLS certificate. If set to `null`, then the Nginx default is respected. |
| `write_timeout` _integer_ | The timeout in milliseconds between two successive write operations for transmitting a request to the upstream server. |


_Appears in:_
- [KongService](#kongservice)



#### KongTargetAPISpec


KongTargetAPISpec are the attributes of the Kong Target itself.



| Field | Description |
| --- | --- |
| `target` _string_ | Target is the target address of the upstream. |
| `weight` _integer_ | Weight is the weight this target gets within the upstream loadbalancer. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the Target for grouping and filtering. |


_Appears in:_
- [KongTargetSpec](#kongtargetspec)

#### KongTargetSpec


KongTargetSpec defines the spec of KongTarget.



| Field | Description |
| --- | --- |
| `upstreamRef` _[NameRef](#nameref)_ | UpstreamRef is a reference to a KongUpstream this KongTarget is attached to. |
| `target` _string_ | Target is the target address of the upstream. |
| `weight` _integer_ | Weight is the weight this target gets within the upstream loadbalancer. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of strings associated with the Target for grouping and filtering. |


_Appears in:_
- [KongTarget](#kongtarget)



#### KongUpstreamAPISpec


KongUpstreamAPISpec defines specification of a Kong Upstream.



| Field | Description |
| --- | --- |
| `algorithm` _[UpstreamAlgorithm](#upstreamalgorithm)_ | Which load balancing algorithm to use. |
| `client_certificate` _[UpstreamClientCertificate](#upstreamclientcertificate)_ | If set, the certificate to be used as client certificate while TLS handshaking to the upstream server. |
| `hash_fallback` _[HashFallback](#hashfallback)_ | What to use as hashing input if the primary `hash_on` does not return a hash (eg. header is missing, or no Consumer identified). Not available if `hash_on` is set to `cookie`. |
| `hash_fallback_header` _string_ | The header name to take the value from as hash input. Only required when `hash_fallback` is set to `header`. |
| `hash_fallback_query_arg` _string_ | The name of the query string argument to take the value from as hash input. Only required when `hash_fallback` is set to `query_arg`. |
| `hash_fallback_uri_capture` _string_ | The name of the route URI capture to take the value from as hash input. Only required when `hash_fallback` is set to `uri_capture`. |
| `hash_on` _[HashOn](#hashon)_ | What to use as hashing input. Using `none` results in a weighted-round-robin scheme with no hashing. |
| `hash_on_cookie` _string_ | The cookie name to take the value from as hash input. Only required when `hash_on` or `hash_fallback` is set to `cookie`. If the specified cookie is not in the request, Kong will generate a value and set the cookie in the response. |
| `hash_on_cookie_path` _string_ | The cookie path to set in the response headers. Only required when `hash_on` or `hash_fallback` is set to `cookie`. |
| `hash_on_header` _string_ | The header name to take the value from as hash input. Only required when `hash_on` is set to `header`. |
| `hash_on_query_arg` _string_ | The name of the query string argument to take the value from as hash input. Only required when `hash_on` is set to `query_arg`. |
| `hash_on_uri_capture` _string_ | The name of the route URI capture to take the value from as hash input. Only required when `hash_on` is set to `uri_capture`. |
| `healthchecks` _[Healthchecks](#healthchecks)_ |  |
| `host_header` _string_ | The hostname to be used as `Host` header when proxying requests through Kong. |
| `name` _string_ | This is a hostname, which must be equal to the `host` of a Service. |
| `slots` _integer_ | The number of slots in the load balancer algorithm. If `algorithm` is set to `round-robin`, this setting determines the maximum number of slots. If `algorithm` is set to `consistent-hashing`, this setting determines the actual number of slots in the algorithm. Accepts an integer in the range `10`-`65536`. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Upstream for grouping and filtering. |
| `use_srv_name` _boolean_ | If set, the balancer will use SRV hostname(if DNS Answer has SRV record) as the proxy upstream `Host`. |


_Appears in:_
- [KongUpstreamSpec](#kongupstreamspec)

#### KongUpstreamSpec


KongUpstreamSpec defines the spec of Kong Upstream.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KongUpstream is associated with. |
| `algorithm` _[UpstreamAlgorithm](#upstreamalgorithm)_ | Which load balancing algorithm to use. |
| `client_certificate` _[UpstreamClientCertificate](#upstreamclientcertificate)_ | If set, the certificate to be used as client certificate while TLS handshaking to the upstream server. |
| `hash_fallback` _[HashFallback](#hashfallback)_ | What to use as hashing input if the primary `hash_on` does not return a hash (eg. header is missing, or no Consumer identified). Not available if `hash_on` is set to `cookie`. |
| `hash_fallback_header` _string_ | The header name to take the value from as hash input. Only required when `hash_fallback` is set to `header`. |
| `hash_fallback_query_arg` _string_ | The name of the query string argument to take the value from as hash input. Only required when `hash_fallback` is set to `query_arg`. |
| `hash_fallback_uri_capture` _string_ | The name of the route URI capture to take the value from as hash input. Only required when `hash_fallback` is set to `uri_capture`. |
| `hash_on` _[HashOn](#hashon)_ | What to use as hashing input. Using `none` results in a weighted-round-robin scheme with no hashing. |
| `hash_on_cookie` _string_ | The cookie name to take the value from as hash input. Only required when `hash_on` or `hash_fallback` is set to `cookie`. If the specified cookie is not in the request, Kong will generate a value and set the cookie in the response. |
| `hash_on_cookie_path` _string_ | The cookie path to set in the response headers. Only required when `hash_on` or `hash_fallback` is set to `cookie`. |
| `hash_on_header` _string_ | The header name to take the value from as hash input. Only required when `hash_on` is set to `header`. |
| `hash_on_query_arg` _string_ | The name of the query string argument to take the value from as hash input. Only required when `hash_on` is set to `query_arg`. |
| `hash_on_uri_capture` _string_ | The name of the route URI capture to take the value from as hash input. Only required when `hash_on` is set to `uri_capture`. |
| `healthchecks` _[Healthchecks](#healthchecks)_ |  |
| `host_header` _string_ | The hostname to be used as `Host` header when proxying requests through Kong. |
| `name` _string_ | This is a hostname, which must be equal to the `host` of a Service. |
| `slots` _integer_ | The number of slots in the load balancer algorithm. If `algorithm` is set to `round-robin`, this setting determines the maximum number of slots. If `algorithm` is set to `consistent-hashing`, this setting determines the actual number of slots in the algorithm. Accepts an integer in the range `10`-`65536`. |
| `tags` _[Tags](#tags)_ | An optional set of strings associated with the Upstream for grouping and filtering. |
| `use_srv_name` _boolean_ | If set, the balancer will use SRV hostname(if DNS Answer has SRV record) as the proxy upstream `Host`. |


_Appears in:_
- [KongUpstream](#kongupstream)



#### KongVaultSpec


KongVaultSpec defines specification of a custom Kong vault.



| Field | Description |
| --- | --- |
| `backend` _string_ | Backend is the type of the backend storing the secrets in the vault. The supported backends of Kong is listed here: https://docs.konghq.com/gateway/latest/kong-enterprise/secrets-management/backends/ |
| `prefix` _string_ | Prefix is the prefix of vault URI for referencing values in the vault. It is immutable after created. |
| `description` _string_ | Description is the additional information about the vault. |
| `config` _[JSON](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#json-v1-apiextensions-k8s-io)_ | Config is the configuration of the vault. Varies for different backends. |
| `tags` _[Tags](#tags)_ | Tags are the tags associated to the vault for grouping and filtering. |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a Konnect ControlPlane this KongVault is associated with. |


_Appears in:_
- [KongVault](#kongvault)



#### KonnectNamespacedRef


KonnectNamespacedRef is the schema for the KonnectNamespacedRef type.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Konnect Control Plane. |
| `namespace` _string_ | Namespace is the namespace where the Konnect Control Plane is in. Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`. |


_Appears in:_
- [ControlPlaneRef](#controlplaneref)
- [KonnectExtensionControlPlaneRef](#konnectextensioncontrolplaneref)

#### Namespace
_Underlying type:_ `string`

Namespace refers to a Kubernetes namespace. It must be a RFC 1123 label.





_Appears in:_
- [ControllerReference](#controllerreference)

#### ObjectName
_Underlying type:_ `string`

ObjectName refers to the name of a Kubernetes object.
Object names can have a variety of forms, including RFC1123 subdomains,
RFC 1123 labels, or RFC 1035 labels.





_Appears in:_
- [ControllerReference](#controllerreference)

#### ObjectReference


ObjectReference defines reference of a kubernetes object.



| Field | Description |
| --- | --- |
| `group` _string_ | Group defines the API group of the referred object. |
| `kind` _string_ | Kind defines the kind of the referred object. |
| `namespace` _string_ | Empty namespace means the same namespace of the owning object. |
| `name` _string_ | Name defines the name of the referred object. |


_Appears in:_
- [KongCustomEntitySpec](#kongcustomentityspec)

#### PEMKeyPair


PEMKeyPair defines a keypair in PEM format.



| Field | Description |
| --- | --- |
| `private_key` _string_ | The private key in PEM format. |
| `public_key` _string_ | The public key in PEM format. |


_Appears in:_
- [KongKeyAPISpec](#kongkeyapispec)
- [KongKeySpec](#kongkeyspec)

#### PluginRef


PluginRef is a reference to a KongPlugin or KongClusterPlugin resource.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the KongPlugin or KongClusterPlugin resource. |
| `kind` _string_ | Kind can be KongPlugin or KongClusterPlugin. If not set, it is assumed to be KongPlugin. |


_Appears in:_
- [KongPluginBindingSpec](#kongpluginbindingspec)

#### ServiceRef


ServiceRef is a reference to a KongService.



| Field | Description |
| --- | --- |
| `type` _string_ | Type can be one of: - namespacedRef |
| `namespacedRef` _[NameRef](#nameref)_ | NamespacedRef is a reference to a KongService. |


_Appears in:_
- [KongRouteSpec](#kongroutespec)

#### TargetRef


TargetRef is a reference based on the object's name.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the entity. |


_Appears in:_
- [KongPluginBindingTargets](#kongpluginbindingtargets)

#### TargetRefWithGroupKind


TargetRefWithGroupKind is a reference based on the object's group, kind, and name.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the entity. |
| `kind` _string_ |  |
| `group` _string_ |  |


_Appears in:_
- [KongPluginBindingTargets](#kongpluginbindingtargets)


## configuration.konghq.com/v1beta1

Package v1beta1 contains API Schema definitions for the configuration.konghq.com v1beta1 API group.

- [KongConsumerGroup](#kongconsumergroup)
- [KongUpstreamPolicy](#kongupstreampolicy)
- [TCPIngress](#tcpingress)
- [UDPIngress](#udpingress)
### KongConsumerGroup


KongConsumerGroup is the Schema for the kongconsumergroups API.

<!-- kong_consumer_group description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1beta1`
| `kind` _string_ | `KongConsumerGroup`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongConsumerGroupSpec](#kongconsumergroupspec)_ |  |



### KongUpstreamPolicy


KongUpstreamPolicy allows configuring algorithm that should be used for load balancing traffic between Kong
Upstream's Targets. It also allows configuring health checks for Kong Upstream's Targets.<br /><br />
Its configuration is similar to Kong Upstream object (https://docs.konghq.com/gateway/latest/admin-api/#upstream-object),
and it is applied to Kong Upstream objects created by the controller.<br /><br />
It can be attached to Services. To attach it to a Service, it has to be annotated with
`konghq.com/upstream-policy: <name>`, where `<name>` is the name of the KongUpstreamPolicy
object in the same namespace as the Service.<br /><br />
When attached to a Service, it will affect all Kong Upstreams created for the Service.<br /><br />
When attached to a Service used in a Gateway API *Route rule with multiple BackendRefs, all of its Services MUST
be configured with the same KongUpstreamPolicy. Otherwise, the controller will *ignore* the KongUpstreamPolicy.<br /><br />
Note: KongUpstreamPolicy doesn't implement Gateway API's GEP-713 strictly.
In particular, it doesn't use the TargetRef for attaching to Services and Gateway API *Routes - annotations are
used instead. This is to allow reusing the same KongUpstreamPolicy for multiple Services and Gateway API *Routes.

<!-- kong_upstream_policy description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1beta1`
| `kind` _string_ | `KongUpstreamPolicy`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongUpstreamPolicySpec](#kongupstreampolicyspec)_ | Spec contains the configuration of the Kong upstream. |



### TCPIngress


TCPIngress is the Schema for the tcpingresses API.
Deprecated: Use Gateway API instead.

<!-- tcp_ingress description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1beta1`
| `kind` _string_ | `TCPIngress`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[TCPIngressSpec](#tcpingressspec)_ | Spec is the TCPIngress specification. |



### UDPIngress


UDPIngress is the Schema for the udpingresses API.
Deprecated: Use Gateway API instead.

<!-- udp_ingress description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `configuration.konghq.com/v1beta1`
| `kind` _string_ | `UDPIngress`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[UDPIngressSpec](#udpingressspec)_ | Spec is the UDPIngress specification. |



### Types

In this section you will find types that the CRDs rely on.
#### HTTPStatus
_Underlying type:_ `integer`

HTTPStatus is an HTTP status code.





_Appears in:_
- [KongUpstreamHealthcheckHealthy](#kongupstreamhealthcheckhealthy)
- [KongUpstreamHealthcheckUnhealthy](#kongupstreamhealthcheckunhealthy)

#### HashInput
_Underlying type:_ `string`

HashInput is the input for consistent-hashing load balancing algorithm.
Use "none" to disable hashing, it is required for sticky sessions.





_Appears in:_
- [KongUpstreamHash](#kongupstreamhash)

#### IngressBackend


IngressBackend describes all endpoints for a given service and port.



| Field | Description |
| --- | --- |
| `serviceName` _string_ | Specifies the name of the referenced service. |
| `servicePort` _integer_ | Specifies the port of the referenced service. |


_Appears in:_
- [IngressRule](#ingressrule)
- [UDPIngressRule](#udpingressrule)

#### IngressRule


IngressRule represents a rule to apply against incoming requests.
Matching is performed based on an (optional) SNI and port.



| Field | Description |
| --- | --- |
| `host` _string_ | Host is the fully qualified domain name of a network host, as defined by RFC 3986. If a Host is not specified, then port-based TCP routing is performed. Kong doesn't care about the content of the TCP stream in this case. If a Host is specified, the protocol must be TLS over TCP. A plain-text TCP request cannot be routed based on Host. It can only be routed based on Port. |
| `port` _integer_ | Port is the port on which to accept TCP or TLS over TCP sessions and route. It is a required field. If a Host is not specified, the requested are routed based only on Port. |
| `backend` _[IngressBackend](#ingressbackend)_ | Backend defines the referenced service endpoint to which the traffic will be forwarded to. |


_Appears in:_
- [TCPIngressSpec](#tcpingressspec)

#### IngressTLS


IngressTLS describes the transport layer security.



| Field | Description |
| --- | --- |
| `hosts` _string array_ | Hosts are a list of hosts included in the TLS certificate. The values in this list must match the name/s used in the tlsSecret. Defaults to the wildcard host setting for the loadbalancer controller fulfilling this Ingress, if left unspecified. |
| `secretName` _string_ | SecretName is the name of the secret used to terminate SSL traffic. |


_Appears in:_
- [TCPIngressSpec](#tcpingressspec)

#### KongConsumerGroupSpec


KongConsumerGroupSpec defines the desired state of KongConsumerGroup.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the ConsumerGroup in Kong. |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this ConsumerGroup is associated with. |
| `tags` _[Tags](#tags)_ | Tags is an optional set of tags applied to the ConsumerGroup. |


_Appears in:_
- [KongConsumerGroup](#kongconsumergroup)



#### KongUpstreamActiveHealthcheck


KongUpstreamActiveHealthcheck configures active health check probing.



| Field | Description |
| --- | --- |
| `type` _string_ | Type determines whether to perform active health checks using HTTP or HTTPS, or just attempt a TCP connection. Accepted values are "http", "https", "tcp", "grpc", "grpcs". |
| `concurrency` _integer_ | Concurrency is the number of targets to check concurrently. |
| `healthy` _[KongUpstreamHealthcheckHealthy](#kongupstreamhealthcheckhealthy)_ | Healthy configures thresholds and HTTP status codes to mark targets healthy for an upstream. |
| `unhealthy` _[KongUpstreamHealthcheckUnhealthy](#kongupstreamhealthcheckunhealthy)_ | Unhealthy configures thresholds and HTTP status codes to mark targets unhealthy for an upstream. |
| `httpPath` _string_ | HTTPPath is the path to use in GET HTTP request to run as a probe. |
| `httpsSni` _string_ | HTTPSSNI is the SNI to use in GET HTTPS request to run as a probe. |
| `httpsVerifyCertificate` _boolean_ | HTTPSVerifyCertificate is a boolean value that indicates if the certificate should be verified. |
| `timeout` _integer_ | Timeout is the probe timeout in seconds. |
| `headers` _object (keys:string, values:string array)_ | Headers is a list of HTTP headers to add to the probe request. |


_Appears in:_
- [KongUpstreamHealthcheck](#kongupstreamhealthcheck)

#### KongUpstreamHash


KongUpstreamHash defines how to calculate hash for consistent-hashing load balancing algorithm.
Only one of the fields must be set.



| Field | Description |
| --- | --- |
| `input` _[HashInput](#hashinput)_ | Input allows using one of the predefined inputs (ip, consumer, path, none). Set this to `none` if you want to use sticky sessions. For other parametrized inputs, use one of the fields below. |
| `header` _string_ | Header is the name of the header to use as hash input. |
| `cookie` _string_ | Cookie is the name of the cookie to use as hash input. |
| `cookiePath` _string_ | CookiePath is cookie path to set in the response headers. |
| `queryArg` _string_ | QueryArg is the name of the query argument to use as hash input. |
| `uriCapture` _string_ | URICapture is the name of the URI capture group to use as hash input. |


_Appears in:_
- [KongUpstreamPolicySpec](#kongupstreampolicyspec)

#### KongUpstreamHealthcheck


KongUpstreamHealthcheck represents a health-check config of an Upstream in Kong.



| Field | Description |
| --- | --- |
| `active` _[KongUpstreamActiveHealthcheck](#kongupstreamactivehealthcheck)_ | Active configures active health check probing. |
| `passive` _[KongUpstreamPassiveHealthcheck](#kongupstreampassivehealthcheck)_ | Passive configures passive health check probing. |
| `threshold` _integer_ | Threshold is the minimum percentage of the upstream’s targets’ weight that must be available for the whole upstream to be considered healthy. |


_Appears in:_
- [KongUpstreamPolicySpec](#kongupstreampolicyspec)

#### KongUpstreamHealthcheckHealthy


KongUpstreamHealthcheckHealthy configures thresholds and HTTP status codes to mark targets healthy for an upstream.



| Field | Description |
| --- | --- |
| `httpStatuses` _[HTTPStatus](#httpstatus) array_ | HTTPStatuses is a list of HTTP status codes that Kong considers a success. |
| `interval` _integer_ | Interval is the interval between active health checks for an upstream in seconds when in a healthy state. |
| `successes` _integer_ | Successes is the number of successes to consider a target healthy. |


_Appears in:_
- [KongUpstreamActiveHealthcheck](#kongupstreamactivehealthcheck)
- [KongUpstreamPassiveHealthcheck](#kongupstreampassivehealthcheck)

#### KongUpstreamHealthcheckUnhealthy


KongUpstreamHealthcheckUnhealthy configures thresholds and HTTP status codes to mark targets unhealthy.



| Field | Description |
| --- | --- |
| `httpFailures` _integer_ | HTTPFailures is the number of failures to consider a target unhealthy. |
| `httpStatuses` _[HTTPStatus](#httpstatus) array_ | HTTPStatuses is a list of HTTP status codes that Kong considers a failure. |
| `tcpFailures` _integer_ | TCPFailures is the number of TCP failures in a row to consider a target unhealthy. |
| `timeouts` _integer_ | Timeouts is the number of timeouts in a row to consider a target unhealthy. |
| `interval` _integer_ | Interval is the interval between active health checks for an upstream in seconds when in an unhealthy state. |


_Appears in:_
- [KongUpstreamActiveHealthcheck](#kongupstreamactivehealthcheck)
- [KongUpstreamPassiveHealthcheck](#kongupstreampassivehealthcheck)

#### KongUpstreamPassiveHealthcheck


KongUpstreamPassiveHealthcheck configures passive checks around
passive health checks.



| Field | Description |
| --- | --- |
| `type` _string_ | Type determines whether to perform passive health checks interpreting HTTP/HTTPS statuses, or just check for TCP connection success. Accepted values are "http", "https", "tcp", "grpc", "grpcs". |
| `healthy` _[KongUpstreamHealthcheckHealthy](#kongupstreamhealthcheckhealthy)_ | Healthy configures thresholds and HTTP status codes to mark targets healthy for an upstream. |
| `unhealthy` _[KongUpstreamHealthcheckUnhealthy](#kongupstreamhealthcheckunhealthy)_ | Unhealthy configures thresholds and HTTP status codes to mark targets unhealthy. |


_Appears in:_
- [KongUpstreamHealthcheck](#kongupstreamhealthcheck)

#### KongUpstreamPolicySpec


KongUpstreamPolicySpec contains the specification for KongUpstreamPolicy.



| Field | Description |
| --- | --- |
| `algorithm` _string_ | Algorithm is the load balancing algorithm to use. Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency", "sticky-sessions. |
| `slots` _integer_ | Slots is the number of slots in the load balancer algorithm. If not set, the default value in Kong for the algorithm is used. |
| `hashOn` _[KongUpstreamHash](#kongupstreamhash)_ | HashOn defines how to calculate hash for consistent-hashing or sticky-sessions load balancing algorithm. Algorithm must be set to "consistent-hashing" or "sticky-sessions" for this field to have effect. |
| `hashOnFallback` _[KongUpstreamHash](#kongupstreamhash)_ | HashOnFallback defines how to calculate hash for consistent-hashing load balancing algorithm if the primary hash function fails. Algorithm must be set to "consistent-hashing" for this field to have effect. |
| `healthchecks` _[KongUpstreamHealthcheck](#kongupstreamhealthcheck)_ | Healthchecks defines the health check configurations in Kong. |
| `stickySessions` _[KongUpstreamStickySessions](#kongupstreamstickysessions)_ | StickySessions defines the sticky session configuration for the upstream. When enabled, clients will be routed to the same backend target based on a cookie. This requires Kong Enterprise Gateway and setting `hash_on` to `none`. |


_Appears in:_
- [KongUpstreamPolicy](#kongupstreampolicy)

#### KongUpstreamStickySessions


KongUpstreamStickySessions defines the sticky session configuration for Kong upstream.
Sticky sessions ensure that requests from the same client are routed to the same backend target.
This is achieved using cookies and requires Kong Enterprise Gateway.



| Field | Description |
| --- | --- |
| `cookie` _string_ | Cookie is the name of the cookie to use for sticky sessions. Kong will generate this cookie if it doesn't exist in the request. |
| `cookiePath` _string_ | CookiePath is the path to set in the cookie. |


_Appears in:_
- [KongUpstreamPolicySpec](#kongupstreampolicyspec)

#### TCPIngressSpec


TCPIngressSpec defines the desired state of TCPIngress.



| Field | Description |
| --- | --- |
| `rules` _[IngressRule](#ingressrule) array_ | A list of rules used to configure the Ingress. |
| `tls` _[IngressTLS](#ingresstls) array_ | TLS configuration. This is similar to the `tls` section in the Ingress resource in networking.v1beta1 group. The mapping of SNIs to TLS cert-key pair defined here will be used for HTTP Ingress rules as well. Once can define the mapping in this resource or the original Ingress resource, both have the same effect. |


_Appears in:_
- [TCPIngress](#tcpingress)



#### UDPIngressRule


UDPIngressRule represents a rule to apply against incoming requests
wherein no Host matching is available for request routing, only the port
is used to match requests.



| Field | Description |
| --- | --- |
| `port` _integer_ | Port indicates the port for the Kong proxy to accept incoming traffic on, which will then be routed to the service Backend. |
| `backend` _[IngressBackend](#ingressbackend)_ | Backend defines the Kubernetes service which accepts traffic from the listening Port defined above. |


_Appears in:_
- [UDPIngressSpec](#udpingressspec)

#### UDPIngressSpec


UDPIngressSpec defines the desired state of UDPIngress.



| Field | Description |
| --- | --- |
| `rules` _[UDPIngressRule](#udpingressrule) array_ | A list of rules used to configure the Ingress. |


_Appears in:_
- [UDPIngress](#udpingress)




## gateway-operator.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the gateway-operator.konghq.com v1alpha1 API group.

- [AIGateway](#aigateway)
- [DataPlaneMetricsExtension](#dataplanemetricsextension)
- [KongPluginInstallation](#kongplugininstallation)
- [KonnectExtension](#konnectextension)
- [WatchNamespaceGrant](#watchnamespacegrant)
### AIGateway


AIGateway is a network Gateway enabling access and management for AI &
Machine Learning models such as Large Language Models (LLM).<br /><br />
The underlying technology for the AIGateway is the Kong Gateway configured
with a variety of plugins which provide the the AI featureset.<br /><br />
This is a list of the plugins, which are available in Kong Gateway v3.6.x+:<br /><br />
  - ai-proxy (https://github.com/kong/kong/tree/master/kong/plugins/ai-proxy)
  - ai-request-transformer (https://github.com/kong/kong/tree/master/kong/plugins/ai-request-transformer)
  - ai-response-transformers (https://github.com/kong/kong/tree/master/kong/plugins/ai-response-transformer)
  - ai-prompt-template (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-template)
  - ai-prompt-guard-plugin (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-guard)
  - ai-prompt-decorator-plugin (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-decorator)<br /><br />
So effectively the AIGateway resource provides a bespoke Gateway resource
(which it owns and manages) with the gateway, consumers and plugin
configurations automated and configurable via Kubernetes APIs.<br /><br />
The current iteration only supports the proxy itself, but the API is being
built with room for future growth in several dimensions. For instance:<br /><br />
  - Supporting auxiliary functions (e.g. decorator, guard, templater, token-rate-limit)
  - Supporting request/response transformers
  - Supporting more than just LLMs (e.g. CCNs, GANs, e.t.c.)
  - Supporting more hosting options for LLMs (e.g. self hosted)
  - Supporting more AI cloud providers
  - Supporting more AI cloud provider features<br /><br />
The validation rules throughout are set up to ensure at least one
cloud-provider-based LLM is specified, but in the future when we have more
model types and more hosting options for those types so we may want to look
into using CEL validation to ensure that at least one model configuration is
provided. We may also want to use CEL to validate things like identifier
unique-ness, e.t.c.<br /><br />
See: https://kubernetes.io/docs/reference/using-api/cel/

<!-- ai_gateway description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `AIGateway`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[AIGatewaySpec](#aigatewayspec)_ | Spec is the desired state of the AIGateway. |



### DataPlaneMetricsExtension


DataPlaneMetricsExtension holds the configuration for the DataPlane metrics extension.
It can be attached to a ControlPlane using its spec.extensions.
When attached it will make the ControlPlane configure its DataPlane with
the specified metrics configuration.
Additionally, it will also make the operator expose DataPlane's metrics
enriched with metadata required for in-cluster Kubernetes autoscaling.

<!-- data_plane_metrics_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `DataPlaneMetricsExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)_ |  |



### KongPluginInstallation


KongPluginInstallation allows using a custom Kong Plugin distributed as a container image available in a registry.
Such a plugin can be associated with GatewayConfiguration or DataPlane to be available for particular Kong Gateway
and configured with KongPlugin CRD.

<!-- kong_plugin_installation description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `KongPluginInstallation`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongPluginInstallationSpec](#kongplugininstallationspec)_ |  |



### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API,
and is intended to be referenced as extension by the DataPlane API.
If a DataPlane successfully refers a KonnectExtension, the DataPlane
deployment spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### WatchNamespaceGrant


WatchNamespaceGrant is a grant that allows a trusted namespace to watch
resources in the namespace this grant exists in.

<!-- watch_namespace_grant description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `WatchNamespaceGrant`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[WatchNamespaceGrantSpec](#watchnamespacegrantspec)_ | Spec is the desired state of the WatchNamespaceGrant. |



### Types

In this section you will find types that the CRDs rely on.
#### AICloudProvider


AICloudProvider is the organization that provides API access to Large Language
Models (LLMs).



| Field | Description |
| --- | --- |
| `name` _[AICloudProviderName](#aicloudprovidername)_ | Name is the unique name of an LLM provider. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### AICloudProviderAPITokenRef


AICloudProviderAPITokenRef is an reference to another object which contains
the API token for an AI cloud provider.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the reference object. |
| `namespace` _string_ | Namespace is the namespace of the reference object.<br /><br /> If not specified, it will be assumed to be the same namespace as the object which references it. |
| `kind` _string_ | Kind is the API object kind<br /><br /> If not specified, it will be assumed to be "Secret". If a Secret is used as the Kind, the secret must contain a single key-value pair where the value is the secret API token. The key can be named anything, as long as there's only one entry, but by convention it should be "apiToken". |


_Appears in:_
- [AIGatewaySpec](#aigatewayspec)

#### AICloudProviderName
_Underlying type:_ `string`

AICloudProviderName indicates the unique name of a supported AI cloud
provider.





_Appears in:_
- [AICloudProvider](#aicloudprovider)

#### AIGatewayConsumerRef


AIGatewayConsumerRef indicates the Secret resource containing the credentials
for the Kong consumer.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the reference object. |
| `namespace` _string_ | Namespace is the namespace of the reference object. |


_Appears in:_
- [AIGatewayEndpoint](#aigatewayendpoint)

#### AIGatewayEndpoint


AIGatewayEndpoint is a network endpoint for accessing an AIGateway.



| Field | Description |
| --- | --- |
| `network` _[EndpointNetworkAccessHint](#endpointnetworkaccesshint)_ | NetworkAccessHint is a hint to the user about what kind of network access is expected for the reachability of this endpoint. |
| `url` _string_ | URL is the URL to access the endpoint from the network indicated by the NetworkAccessHint. |
| `models` _string array_ | AvailableModels is a list of the identifiers of all the AI models that are accessible from this endpoint. |
| `consumer` _[AIGatewayConsumerRef](#aigatewayconsumerref)_ | Consumer is a reference to the Secret that contains the credentials for the Kong consumer that is allowed to access this endpoint. |
| `conditions` _[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#condition-v1-meta) array_ | Conditions describe the current conditions of the AIGatewayEndpoint.<br /><br /> Known condition types are:<br /><br />   - "Provisioning"   - "EndpointReady" |


_Appears in:_
- [AIGatewayStatus](#aigatewaystatus)

#### AIGatewaySpec


AIGatewaySpec defines the desired state of an AIGateway.



| Field | Description |
| --- | --- |
| `gatewayClassName` _string_ | GatewayClassName is the name of the GatewayClass which is responsible for the AIGateway. |
| `largeLanguageModels` _[LargeLanguageModels](#largelanguagemodels)_ | LargeLanguageModels is a list of Large Language Models (LLMs) to be managed by the AI Gateway.<br /><br /> This is a required field because we only support LLMs at the moment. In future iterations we may support other model types. |
| `cloudProviderCredentials` _[AICloudProviderAPITokenRef](#aicloudproviderapitokenref)_ | CloudProviderCredentials is a reference to an object (e.g. a Kubernetes Secret) which contains the credentials needed to access the APIs of cloud providers.<br /><br /> This is the global configuration that will be used by DEFAULT for all model configurations. A secret configured this way MAY include any number of key-value pairs equal to the number of providers you have, but used this way the keys MUST be named according to their providers (e.g. "openai", "azure", "cohere", e.t.c.). For example:<br /><br />   apiVersion: v1   kind: Secret   metadata:     name: devteam-ai-cloud-providers   type: Opaque   data:     openai: *****************     azure: *****************     cohere: *****************<br /><br /> See AICloudProviderName for a list of known and valid cloud providers.<br /><br /> Note that the keys are NOT case-sensitive (e.g. "OpenAI", "openai", and "openAI" are all valid and considered the same keys) but if there are duplicates endpoints failures conditions will be emitted and endpoints will not be configured until the duplicates are resolved.<br /><br /> This is currently considered required, but in future iterations will be optional as we do things like enable configuring credentials at the model level. |


_Appears in:_
- [AIGateway](#aigateway)



#### CloudHostedLargeLanguageModel


CloudHostedLargeLanguageModel is the configuration for Large Language Models
(LLM) hosted by a known and supported AI cloud provider (e.g. OpenAI, Cohere,
Azure, e.t.c.).



| Field | Description |
| --- | --- |
| `identifier` _string_ | Identifier is the unique name which identifies the LLM. This will be used as part of the requests made to an AIGateway endpoint. For instance: if you provided the identifier "devteam-gpt-access", then you would access this model via "https://${endpoint}/devteam-gpt-access" and supply it with your consumer credentials to authenticate requests. |
| `model` _string_ | Model is the model name of the LLM (e.g. gpt-3.5-turbo, phi-2, e.t.c.).<br /><br /> If not specified, whatever the cloud provider specifies as the default model will be used. |
| `promptType` _[LLMPromptType](#llmprompttype)_ | PromptType is the type of prompt to be used for inference requests to the LLM (e.g. "chat", "completions").<br /><br /> If "chat" is specified, prompts sent by the user will be interactive, contextual and stateful. The LLM will dynamically answer questions and simulate a dialogue, while also keeping track of the conversation to provide contextually relevant responses.<br /><br /> If "completions" is specified, prompts sent by the user will be stateless and "one-shot". The LLM will provide a single response to the prompt, without any context from previous prompts.<br /><br /> If not specified, "completions" will be used as the default. |
| `defaultPrompts` _[LLMPrompt](#llmprompt) array_ | DefaultPrompts is a list of prompts that should be provided to the LLM by default. This is generally used to influence inference behavior, for instance by providing a "system" role prompt that instructs the LLM to take on a certain persona. |
| `defaultPromptParams` _[LLMPromptParams](#llmpromptparams)_ | DefaultPromptParams configures the parameters which will be sent with any and every inference request.<br /><br /> If this is set, there is currently no way to override these parameters at the individual prompt level. This is an expected feature from later releases of our AI plugins. |
| `aiCloudProvider` _[AICloudProvider](#aicloudprovider)_ | AICloudProvider defines the cloud provider that will fulfill the LLM requests for this CloudHostedLargeLanguageModel |


_Appears in:_
- [LargeLanguageModels](#largelanguagemodels)

#### ClusterCertificateSecretRef


ClusterCertificateSecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [KonnectControlPlaneAPIAuthConfiguration](#konnectcontrolplaneapiauthconfiguration)

#### DataPlaneMetricsExtensionSpec


DataPlaneMetricsExtensionSpec defines the spec for the DataPlaneMetricsExtension.



| Field | Description |
| --- | --- |
| `serviceSelector` _[ServiceSelector](#serviceselector)_ | ServiceSelector holds the service selector specifying the services for which metrics should be collected. |
| `config` _[MetricsConfig](#metricsconfig)_ | Config holds the configuration for the DataPlane metrics. |


_Appears in:_
- [DataPlaneMetricsExtension](#dataplanemetricsextension)



#### EndpointNetworkAccessHint
_Underlying type:_ `string`

EndpointNetworkAccessHint provides a human readable indication of what kind
of network access is expected for a Gateway.<br /><br />
This isn't meant to reflect knowledge of any specific network by name, which
is why it includes "hint" in the name. It's meant to be a hint to the user
such as "internet-accessible", "internal-only".





_Appears in:_
- [AIGatewayEndpoint](#aigatewayendpoint)





#### KongPluginInstallationSpec


KongPluginInstallationSpec provides the information necessary to retrieve and install a Kong custom plugin.



| Field | Description |
| --- | --- |
| `image` _string_ | The image is an OCI image URL for a packaged custom Kong plugin. |
| `imagePullSecretRef` _[SecretObjectReference](#secretobjectreference)_ | ImagePullSecretRef is a reference to a Kubernetes Secret containing credentials necessary to pull the OCI image in Image. It must follow the format in https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry. It is optional. If the image is public, omit this field. |


_Appears in:_
- [KongPluginInstallation](#kongplugininstallation)



#### KonnectControlPlaneAPIAuthConfiguration


KonnectControlPlaneAPIAuthConfiguration contains the configuration to authenticate with Konnect API ControlPlane.



| Field | Description |
| --- | --- |
| `clusterCertificateSecretRef` _[ClusterCertificateSecretRef](#clustercertificatesecretref)_ | ClusterCertificateSecretRef is the reference to the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KonnectExtension is associated with. |
| `controlPlaneRegion` _string_ | ControlPlaneRegion is the region of the Konnect Control Plane. |
| `serverHostname` _string_ | ServerHostname is the fully qualified domain name of the Konnect server. For typical operation a default value doesn't need to be adjusted. It matches the RFC 1123 definition of a hostname with 1 notable exception that numeric IP addresses are not allowed.<br /><br /> Note that as per RFC1035 and RFC1123, a *label* must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character. No other punctuation is allowed. |
| `konnectControlPlaneAPIAuthConfiguration` _[KonnectControlPlaneAPIAuthConfiguration](#konnectcontrolplaneapiauthconfiguration)_ | AuthConfiguration must be used to configure the Konnect API authentication. |
| `clusterDataPlaneLabels` _object (keys:string, values:string)_ | ClusterDataPlaneLabels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### LLMPrompt


LLMPrompt is a text prompt that includes parameters, a role and content.<br /><br />
This is intended for situations like when you need to provide roles in a
prompt to an LLM in order to influence its behavior and responses.<br /><br />
For example, you might want to provide a "system" role and tell the LLM
something like "you are a helpful assistant who responds in the style of
Sherlock Holmes".



| Field | Description |
| --- | --- |
| `content` _string_ | Content is the prompt text sent for inference. |
| `role` _[LLMPromptRole](#llmpromptrole)_ | Role indicates the role of the prompt. This is used to identify the prompt's purpose, such as "system" or "user" and can influence the behavior of the LLM.<br /><br /> If not specified, "user" will be used as the default. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LLMPromptParams


LLMPromptParams contains parameters that can be used to control the behavior
of a large language model (LLM) when generating text based on a prompt.



| Field | Description |
| --- | --- |
| `temperature` _string_ | Temperature controls the randomness of predictions by scaling the logits before applying softmax. A lower temperature (e.g., 0.0 to 0.7) makes the model more confident in its predictions, leading to more repetitive and deterministic outputs. A higher temperature (e.g., 0.8 to 1.0) increases randomness, generating more diverse and creative outputs. At very high temperatures, the outputs may become nonsensical or highly unpredictable. |
| `maxTokens` _integer_ | Max Tokens specifies the maximum length of the model's output in terms of the number of tokens (words or pieces of words). This parameter limits the output's size, ensuring the model generates content within a manageable scope. A token can be a word or part of a word, depending on the model's tokenizer. |
| `topK` _integer_ | TopK sampling is a technique where the model's prediction is limited to the K most likely next tokens at each step of the generation process. The probability distribution is truncated to these top K tokens, and the next token is randomly sampled from this subset. This method helps in reducing the chance of selecting highly improbable tokens, making the text more coherent. A smaller K leads to more predictable text, while a larger K allows for more diversity but with an increased risk of incoherence. |
| `topP` _string_ | TopP (also known as nucleus sampling) is an alternative to top K sampling. Instead of selecting the top K tokens, top P sampling chooses from the smallest set of tokens whose cumulative probability exceeds the threshold P. This method dynamically adjusts the number of tokens considered at each step, depending on their probability distribution. It helps in maintaining diversity while also avoiding very unlikely tokens. A higher P value increases diversity but can lead to less coherence, whereas a lower P value makes the model's outputs more focused and coherent. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LLMPromptRole
_Underlying type:_ `string`

LLMPromptRole indicates the role of a prompt for a large language model (LLM).





_Appears in:_
- [LLMPrompt](#llmprompt)

#### LLMPromptType
_Underlying type:_ `string`

LLMPromptType indicates the type of prompt to be used for a large
language model (LLM).





_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LargeLanguageModels


LargeLanguageModels is a list of Large Language Models (LLM) hosted in
various ways (cloud hosted, self hosted, e.t.c.) which the AIGateway should
serve and manage traffic for.



| Field | Description |
| --- | --- |
| `cloudHosted` _[CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel) array_ | CloudHosted configures LLMs hosted and served by cloud providers.<br /><br /> This is currently a required field, requiring at least one cloud-hosted LLM be specified, however in future iterations we may add other hosting options such as self-hosted LLMs as separate fields. |


_Appears in:_
- [AIGatewaySpec](#aigatewayspec)

#### MetricsConfig


MetricsConfig holds the configuration for the DataPlane metrics.



| Field | Description |
| --- | --- |
| `latency` _boolean_ | Latency indicates whether latency metrics are enabled for the DataPlane. This translates into deployed instances having `latency_metrics` option set on the Prometheus plugin. |
| `bandwidth` _boolean_ | Bandwidth indicates whether bandwidth metrics are enabled for the DataPlane. This translates into deployed instances having `bandwidth_metrics` option set on the Prometheus plugin. |
| `upstreamHealth` _boolean_ | UpstreamHealth indicates whether upstream health metrics are enabled for the DataPlane. This translates into deployed instances having `upstream_health_metrics` option set on the Prometheus plugin. |
| `statusCode` _boolean_ | StatusCode indicates whether status code metrics are enabled for the DataPlane. This translates into deployed instances having `status_code_metrics` option set on the Prometheus plugin. |


_Appears in:_
- [DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)

#### ServiceSelector


ServiceSelector holds the service selector specification.



| Field | Description |
| --- | --- |
| `matchNames` _[ServiceSelectorEntry](#serviceselectorentry) array_ | MatchNames holds the list of Services names to match. |


_Appears in:_
- [DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)

#### ServiceSelectorEntry


ServiceSelectorEntry holds the name of a service to match.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the service to match. |


_Appears in:_
- [ServiceSelector](#serviceselector)

#### WatchNamespaceGrantFrom


WatchNamespaceGrantFrom describes trusted namespaces.



| Field | Description |
| --- | --- |
| `group` _string_ | Group is the group of the referent. |
| `kind` _string_ | Kind is the kind of the referent. |
| `namespace` _string_ | Namespace is the namespace of the referent. |


_Appears in:_
- [WatchNamespaceGrantSpec](#watchnamespacegrantspec)

#### WatchNamespaceGrantSpec


WatchNamespaceGrantSpec defines the desired state of an WatchNamespaceGrant.



| Field | Description |
| --- | --- |
| `from` _[WatchNamespaceGrantFrom](#watchnamespacegrantfrom) array_ | From describes the trusted namespaces and kinds that can reference the namespace this grant exists in. |


_Appears in:_
- [WatchNamespaceGrant](#watchnamespacegrant)


## gateway-operator.konghq.com/v1beta1

Package v1beta1 contains API Schema definitions for the gateway-operator.konghq.com v1beta1 API group.

- [ControlPlane](#controlplane)
- [DataPlane](#dataplane)
- [GatewayConfiguration](#gatewayconfiguration)
### ControlPlane


ControlPlane is the Schema for the controlplanes API

<!-- control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `ControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[ControlPlaneSpec](#controlplanespec)_ |  |



### DataPlane


DataPlane is the Schema for the dataplanes API

<!-- data_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `DataPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[DataPlaneSpec](#dataplanespec)_ |  |



### GatewayConfiguration


GatewayConfiguration is the Schema for the gatewayconfigurations API.

<!-- gateway_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `GatewayConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[GatewayConfigurationSpec](#gatewayconfigurationspec)_ |  |



### Types

In this section you will find types that the CRDs rely on.
#### Address


Address describes an address which can be either an IP address or a hostname.



| Field | Description |
| --- | --- |
| `type` _[AddressType](#addresstype)_ | Type of the address. |
| `value` _string_ | Value of the address. The validity of the values will depend on the type and support by the controller.<br /><br /> Examples: `*******`, `128::1`, `my-ip-address`. |
| `sourceType` _[AddressSourceType](#addresssourcetype)_ | Source type of the address. |


_Appears in:_
- [DataPlaneStatus](#dataplanestatus)
- [RolloutStatusService](#rolloutstatusservice)

#### AddressSourceType
_Underlying type:_ `string`

AddressSourceType defines the type of source this address represents.<br /><br />
Can be one of:<br /><br />
* `PublicLoadBalancer`
* `PrivateLoadBalancer`
* `PublicIP`
* `PrivateIP`





_Appears in:_
- [Address](#address)

#### AddressType
_Underlying type:_ `string`

AddressType defines how a network address is represented as a text string.<br /><br />
Can be one of:<br /><br />
* `IPAddress`
* `Hostname`





_Appears in:_
- [Address](#address)

#### BlueGreenStrategy


BlueGreenStrategy defines the Blue Green deployment strategy.



| Field | Description |
| --- | --- |
| `promotion` _[Promotion](#promotion)_ | Promotion defines how the operator handles promotion of resources. |
| `resources` _[RolloutResources](#rolloutresources)_ | Resources controls what happens to operator managed resources during or after a rollout. |


_Appears in:_
- [RolloutStrategy](#rolloutstrategy)

#### ControlPlaneDeploymentOptions


ControlPlaneDeploymentOptions is a shared type used on objects to indicate that their
configuration results in a Deployment which is managed by the Operator and
includes options for managing Deployments such as the the number of replicas
or pod options like container image and resource requirements.
version, as well as Env variable overrides.



| Field | Description |
| --- | --- |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This only affects the DataPlane deployments for now, for more details on ControlPlane scaling please see https://github.com/Kong/gateway-operator/issues/736. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneOptions


ControlPlaneOptions indicates the specific information needed to
deploy and connect a ControlPlane to a DataPlane object.



| Field | Description |
| --- | --- |
| `deployment` _[ControlPlaneDeploymentOptions](#controlplanedeploymentoptions)_ |  |
| `dataplane` _string_ | DataPlanes refers to the named DataPlane objects which this ControlPlane is responsible for. Currently they must be in the same namespace as the DataPlane. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |


_Appears in:_
- [ControlPlaneSpec](#controlplanespec)
- [GatewayConfigurationSpec](#gatewayconfigurationspec)

#### ControlPlaneSpec


ControlPlaneSpec defines the desired state of ControlPlane



| Field | Description |
| --- | --- |
| `deployment` _[ControlPlaneDeploymentOptions](#controlplanedeploymentoptions)_ |  |
| `dataplane` _string_ | DataPlanes refers to the named DataPlane objects which this ControlPlane is responsible for. Currently they must be in the same namespace as the DataPlane. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `gatewayClass` _[ObjectName](#objectname)_ | GatewayClass indicates the Gateway resources which this ControlPlane should be responsible for configuring routes for (e.g. HTTPRoute, TCPRoute, UDPRoute, TLSRoute, e.t.c.).<br /><br /> Required for the ControlPlane to have any effect: at least one Gateway must be present for configuration to be pushed to the data-plane and only Gateway resources can be used to identify data-plane entities. |
| `ingressClass` _string_ | IngressClass enables support for the older Ingress resource and indicates which Ingress resources this ControlPlane should be responsible for.<br /><br /> Routing configured this way will be applied to the Gateway resources indicated by GatewayClass.<br /><br /> If omitted, Ingress resources will not be supported by the ControlPlane. |


_Appears in:_
- [ControlPlane](#controlplane)



#### DataPlaneDeploymentOptions


DataPlaneDeploymentOptions specifies options for the Deployments (as in the Kubernetes
resource "Deployment") which are created and managed for the DataPlane resource.



| Field | Description |
| --- | --- |
| `rollout` _[Rollout](#rollout)_ | Rollout describes a custom rollout strategy. |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This is effectively shorthand for setting a scaling minimum and maximum to the same value. This field and the scaling field are mutually exclusive: You can only configure one or the other. |
| `scaling` _[Scaling](#scaling)_ | Scaling defines the scaling options for the deployment. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. It's being applied on top of the generated Deployments using [StrategicMergePatch](https://pkg.go.dev/k8s.io/apimachinery/pkg/util/strategicpatch#StrategicMergePatch). |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### DataPlaneNetworkOptions


DataPlaneNetworkOptions defines network related options for a DataPlane.



| Field | Description |
| --- | --- |
| `services` _[DataPlaneServices](#dataplaneservices)_ | Services indicates the configuration of Kubernetes Services needed for the topology of various forms of traffic (including ingress, e.t.c.) to and from the DataPlane. |
| `konnectCertificate` _[KonnectCertificateOptions](#konnectcertificateoptions)_ | KonnectCA is the certificate authority that the operator uses to provision client certificates the DataPlane will use to authenticate itself to the Konnect API. Requires Enterprise. |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneOptions


DataPlaneOptions defines the information specifically needed to
deploy the DataPlane.



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[DataPlaneNetworkOptions](#dataplanenetworkoptions)_ |  |
| `resources` _[DataPlaneResources](#dataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the DataPlane. |


_Appears in:_
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneResources


DataPlaneResources defines the resources that will be created and managed
for the DataPlane.



| Field | Description |
| --- | --- |
| `podDisruptionBudget` _[PodDisruptionBudget](#poddisruptionbudget)_ | PodDisruptionBudget is the configuration for the PodDisruptionBudget that will be created for the DataPlane. |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneRolloutStatus


DataPlaneRolloutStatus describes the DataPlane rollout status.



| Field | Description |
| --- | --- |
| `services` _[DataPlaneRolloutStatusServices](#dataplanerolloutstatusservices)_ | Services contain the information about the services which are available through which user can access the preview deployment. |
| `deployment` _[DataPlaneRolloutStatusDeployment](#dataplanerolloutstatusdeployment)_ | Deployment contains the information about the preview deployment. |
| `conditions` _[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#condition-v1-meta) array_ | Conditions contains the status conditions about the rollout. |


_Appears in:_
- [DataPlaneStatus](#dataplanestatus)

#### DataPlaneRolloutStatusDeployment


DataPlaneRolloutStatusDeployment is a rollout status field which contains
fields specific for Deployments during the rollout.



| Field | Description |
| --- | --- |
| `selector` _string_ | Selector is a stable label selector value assigned to a DataPlane rollout status which is used throughout the rollout as a deterministic labels selector for Services and Deployments. |


_Appears in:_
- [DataPlaneRolloutStatus](#dataplanerolloutstatus)

#### DataPlaneRolloutStatusServices


DataPlaneRolloutStatusServices describes the status of the services during
DataPlane rollout.



| Field | Description |
| --- | --- |
| `ingress` _[RolloutStatusService](#rolloutstatusservice)_ | Ingress contains the name and the address of the preview service for ingress. Using this service users can send requests that will hit the preview deployment. |
| `adminAPI` _[RolloutStatusService](#rolloutstatusservice)_ | AdminAPI contains the name and the address of the preview service for Admin API. Using this service users can send requests to configure the DataPlane's preview deployment. |


_Appears in:_
- [DataPlaneRolloutStatus](#dataplanerolloutstatus)

#### DataPlaneServiceOptions


DataPlaneServiceOptions contains Services related DataPlane configuration.



| Field | Description |
| --- | --- |
| `ports` _[DataPlaneServicePort](#dataplaneserviceport) array_ | Ports defines the list of ports that are exposed by the service. The ports field allows defining the name, port and targetPort of the underlying service ports, while the protocol is defaulted to TCP, as it is the only protocol currently supported. |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [DataPlaneServices](#dataplaneservices)

#### DataPlaneServicePort


DataPlaneServicePort contains information on service's port.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of this port within the service. This must be a DNS_LABEL. All ports within a ServiceSpec must have unique names. When considering the endpoints for a Service, this must match the 'name' field in the EndpointPort. Optional if only one ServicePort is defined on this service. |
| `port` _integer_ | The port that will be exposed by this service. |
| `targetPort` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | Number or name of the port to access on the pods targeted by the service. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME. If this is a string, it will be looked up as a named port in the target Pod's container ports. If this is not specified, the value of the 'port' field is used (an identity map). This field is ignored for services with clusterIP=None, and should be omitted or set equal to the 'port' field. More info: https://kubernetes.io/docs/concepts/services-networking/service/#defining-a-service |
| `nodePort` _integer_ | The port on each node on which this service is exposed when type is NodePort or LoadBalancer. Usually assigned by the system. If a value is specified, in-range, and not in use it will be used, otherwise the operation will fail. If not specified, a port will be allocated if this Service requires one. If this field is specified when creating a Service which does not need it, creation will fail. This field will be wiped when updating a Service to no longer need it (e.g. changing type from NodePort to ClusterIP).<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport<br /><br /> Can only be specified if type is NodePort or LoadBalancer. |


_Appears in:_
- [DataPlaneServiceOptions](#dataplaneserviceoptions)

#### DataPlaneServices


DataPlaneServices contains Services related DataPlane configuration, shared with the GatewayConfiguration.



| Field | Description |
| --- | --- |
| `ingress` _[DataPlaneServiceOptions](#dataplaneserviceoptions)_ | Ingress is the Kubernetes Service that will be used to expose ingress traffic for the DataPlane. Here you can determine whether the DataPlane will be exposed outside the cluster (e.g. using a LoadBalancer type Services) or only internally (e.g. ClusterIP), and inject any additional annotations you need on the service (for instance, if you need to influence a cloud provider LoadBalancer configuration). |


_Appears in:_
- [DataPlaneNetworkOptions](#dataplanenetworkoptions)

#### DataPlaneSpec


DataPlaneSpec defines the desired state of DataPlane



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[DataPlaneNetworkOptions](#dataplanenetworkoptions)_ |  |
| `resources` _[DataPlaneResources](#dataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the DataPlane. |


_Appears in:_
- [DataPlane](#dataplane)



#### DeploymentOptions


DeploymentOptions is a shared type used on objects to indicate that their
configuration results in a Deployment which is managed by the Operator and
includes options for managing Deployments such as the number of replicas
or pod options like container image and resource requirements.
version, as well as Env variable overrides.



| Field | Description |
| --- | --- |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This is effectively shorthand for setting a scaling minimum and maximum to the same value. This field and the scaling field are mutually exclusive: You can only configure one or the other. |
| `scaling` _[Scaling](#scaling)_ | Scaling defines the scaling options for the deployment. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. It's being applied on top of the generated Deployments using [StrategicMergePatch](https://pkg.go.dev/k8s.io/apimachinery/pkg/util/strategicpatch#StrategicMergePatch). |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)

#### GatewayConfigDataPlaneNetworkOptions


GatewayConfigDataPlaneNetworkOptions defines network related options for a DataPlane.



| Field | Description |
| --- | --- |
| `services` _[GatewayConfigDataPlaneServices](#gatewayconfigdataplaneservices)_ | Services indicates the configuration of Kubernetes Services needed for the topology of various forms of traffic (including ingress, etc.) to and from the DataPlane. |


_Appears in:_
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### GatewayConfigDataPlaneOptions


GatewayConfigDataPlaneOptions indicates the specific information needed to
configure and deploy a DataPlane object.



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[GatewayConfigDataPlaneNetworkOptions](#gatewayconfigdataplanenetworkoptions)_ |  |
| `resources` _[GatewayConfigDataPlaneResources](#gatewayconfigdataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the Gateways (DataPlanes) that use this GatewayConfig. |


_Appears in:_
- [GatewayConfigurationSpec](#gatewayconfigurationspec)

#### GatewayConfigDataPlaneResources


GatewayConfigDataPlaneResources defines the resources that will be
created and managed for Gateway's DataPlane.



| Field | Description |
| --- | --- |
| `podDisruptionBudget` _[PodDisruptionBudget](#poddisruptionbudget)_ | PodDisruptionBudget is the configuration for the PodDisruptionBudget that will be created for the DataPlane. |


_Appears in:_
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### GatewayConfigDataPlaneServices


GatewayConfigDataPlaneServices contains Services related DataPlane configuration.



| Field | Description |
| --- | --- |
| `ingress` _[GatewayConfigServiceOptions](#gatewayconfigserviceoptions)_ | Ingress is the Kubernetes Service that will be used to expose ingress traffic for the DataPlane. Here you can determine whether the DataPlane will be exposed outside the cluster (e.g. using a LoadBalancer type Services) or only internally (e.g. ClusterIP), and inject any additional annotations you need on the service (for instance, if you need to influence a cloud provider LoadBalancer configuration). |


_Appears in:_
- [GatewayConfigDataPlaneNetworkOptions](#gatewayconfigdataplanenetworkoptions)

#### GatewayConfigServiceOptions


GatewayConfigServiceOptions is used to includes options to customize the ingress service,
such as the annotations.



| Field | Description |
| --- | --- |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [GatewayConfigDataPlaneServices](#gatewayconfigdataplaneservices)

#### GatewayConfigurationSpec


GatewayConfigurationSpec defines the desired state of GatewayConfiguration



| Field | Description |
| --- | --- |
| `dataPlaneOptions` _[GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)_ | DataPlaneOptions is the specification for configuration overrides for DataPlane resources that will be created for the Gateway. |
| `controlPlaneOptions` _[ControlPlaneOptions](#controlplaneoptions)_ | ControlPlaneOptions is the specification for configuration overrides for ControlPlane resources that will be created for the Gateway. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the Gateway resource to influence or enhance functionality. NOTE: currently, there's only 1 extension that can be attached at the Gateway level (KonnectExtension), so the amount of extensions is limited to 1. |


_Appears in:_
- [GatewayConfiguration](#gatewayconfiguration)





#### HorizontalScaling


HorizontalScaling defines horizontal scaling options for the deployment.
It holds all the options from the HorizontalPodAutoscalerSpec besides the
ScaleTargetRef which is being controlled by the Operator.



| Field | Description |
| --- | --- |
| `minReplicas` _integer_ | minReplicas is the lower limit for the number of replicas to which the autoscaler can scale down.  It defaults to 1 pod.  minReplicas is allowed to be 0 if the alpha feature gate HPAScaleToZero is enabled and at least one Object or External metric is configured.  Scaling is active as long as at least one metric value is available. |
| `maxReplicas` _integer_ | maxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up. It cannot be less that minReplicas. |
| `metrics` _[MetricSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#metricspec-v2-autoscaling) array_ | metrics contains the specifications for which to use to calculate the desired replica count (the maximum replica count across all metrics will be used).  The desired replica count is calculated multiplying the ratio between the target value and the current value by the current number of pods.  Ergo, metrics used must decrease as the pod count is increased, and vice-versa.  See the individual metric source types for more information about how each type of metric must respond. If not set, the default metric will be set to 80% average CPU utilization. |
| `behavior` _[HorizontalPodAutoscalerBehavior](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#horizontalpodautoscalerbehavior-v2-autoscaling)_ | behavior configures the scaling behavior of the target in both Up and Down directions (scaleUp and scaleDown fields respectively). If not set, the default HPAScalingRules for scale up and scale down are used. |


_Appears in:_
- [Scaling](#scaling)

#### KonnectCertificateOptions


KonnectCertificateOptions indicates how the operator should manage the certificates that managed entities will use
to interact with Konnect.



| Field | Description |
| --- | --- |
| `issuer` _[NamespacedName](#namespacedname)_ | Issuer is the cert-manager Issuer or ClusterIssuer the operator will use to request certificates. When Namespace is set, the operator will retrieve the Issuer with that Name in that Namespace. When Namespace is omitted, the operator will retrieve the ClusterIssuer with that name. |


_Appears in:_
- [DataPlaneNetworkOptions](#dataplanenetworkoptions)

#### NamespacedName


NamespacedName is a resource identified by name and optional namespace.



| Field | Description |
| --- | --- |
| `namespace` _string_ |  |
| `name` _string_ |  |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)
- [KonnectCertificateOptions](#konnectcertificateoptions)

#### PodDisruptionBudget


PodDisruptionBudget defines the configuration for the PodDisruptionBudget.



| Field | Description |
| --- | --- |
| `spec` _[PodDisruptionBudgetSpec](#poddisruptionbudgetspec)_ | Spec defines the specification of the PodDisruptionBudget. Selector is managed by the controller and cannot be set by the user. |


_Appears in:_
- [DataPlaneResources](#dataplaneresources)
- [GatewayConfigDataPlaneResources](#gatewayconfigdataplaneresources)

#### PodDisruptionBudgetSpec


PodDisruptionBudgetSpec defines the specification of a PodDisruptionBudget.



| Field | Description |
| --- | --- |
| `minAvailable` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | An eviction is allowed if at least "minAvailable" pods selected by "selector" will still be available after the eviction, i.e. even in the absence of the evicted pod.  So for example you can prevent all voluntary evictions by specifying "100%". |
| `maxUnavailable` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | An eviction is allowed if at most "maxUnavailable" pods selected by "selector" are unavailable after the eviction, i.e. even in absence of the evicted pod. For example, one can prevent all voluntary evictions by specifying 0. This is a mutually exclusive setting with "minAvailable". |
| `unhealthyPodEvictionPolicy` _[UnhealthyPodEvictionPolicyType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#unhealthypodevictionpolicytype-v1-policy)_ | UnhealthyPodEvictionPolicy defines the criteria for when unhealthy pods should be considered for eviction. Current implementation considers healthy pods, as pods that have status.conditions item with type="Ready",status="True".<br /><br /> Valid policies are IfHealthyBudget and AlwaysAllow. If no policy is specified, the default behavior will be used, which corresponds to the IfHealthyBudget policy.<br /><br /> IfHealthyBudget policy means that running pods (status.phase="Running"), but not yet healthy can be evicted only if the guarded application is not disrupted (status.currentHealthy is at least equal to status.desiredHealthy). Healthy pods will be subject to the PDB for eviction.<br /><br /> AlwaysAllow policy means that all running pods (status.phase="Running"), but not yet healthy are considered disrupted and can be evicted regardless of whether the criteria in a PDB is met. This means perspective running pods of a disrupted application might not get a chance to become healthy. Healthy pods will be subject to the PDB for eviction.<br /><br /> Additional policies may be added in the future. Clients making eviction decisions should disallow eviction of unhealthy pods if they encounter an unrecognized policy in this field.<br /><br /> This field is beta-level. The eviction API uses this field when the feature gate PDBUnhealthyPodEvictionPolicy is enabled (enabled by default). |


_Appears in:_
- [PodDisruptionBudget](#poddisruptionbudget)

#### Promotion


Promotion is a type that contains fields that define how the operator handles
promotion of resources during a blue/green rollout.



| Field | Description |
| --- | --- |
| `strategy` _[PromotionStrategy](#promotionstrategy)_ | Strategy indicates how you want the operator to handle the promotion of the preview (green) resources (Deployments and Services) after all workflows and tests succeed, OR if you even want it to break before performing the promotion to allow manual inspection. |


_Appears in:_
- [BlueGreenStrategy](#bluegreenstrategy)

#### PromotionStrategy
_Underlying type:_ `string`

PromotionStrategy is the type of promotion strategy consts.<br /><br />
Allowed values:<br /><br />
  - `BreakBeforePromotion` is a promotion strategy which will ensure all new
    resources are ready and then break, to enable manual inspection.
    The user must indicate manually when they want the promotion to continue.
    That can be done by annotating the `DataPlane` object with
    `"gateway-operator.konghq.com/promote-when-ready": "true"`.





_Appears in:_
- [Promotion](#promotion)

#### Rollout


Rollout defines options for rollouts.



| Field | Description |
| --- | --- |
| `strategy` _[RolloutStrategy](#rolloutstrategy)_ | Strategy contains the deployment strategy for rollout. |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)

#### RolloutResourcePlan
_Underlying type:_ `[struct{Deployment RolloutResourcePlanDeployment "json:\"deployment,omitempty\""}](#struct{deployment-rolloutresourceplandeployment-"json:\"deployment,omitempty\""})`

RolloutResourcePlan is a type that holds rollout resource plan related fields
which control how the operator handles resources during and after a rollout.





_Appears in:_
- [RolloutResources](#rolloutresources)



#### RolloutResources


RolloutResources is the type which contains the fields which control how the operator
manages the resources it manages during or after the rollout concludes.



| Field | Description |
| --- | --- |
| `plan` _[RolloutResourcePlan](#rolloutresourceplan)_ | Plan defines the resource plan for managing resources during and after a rollout. |


_Appears in:_
- [BlueGreenStrategy](#bluegreenstrategy)

#### RolloutStatusService


RolloutStatusService is a struct which contains status information about
services that are exposed as part of the rollout.



| Field | Description |
| --- | --- |
| `name` _string_ | Name indicates the name of the service. |
| `addresses` _[Address](#address) array_ | Addresses contains the addresses of a Service. |


_Appears in:_
- [DataPlaneRolloutStatusServices](#dataplanerolloutstatusservices)

#### RolloutStrategy


RolloutStrategy holds the rollout strategy options.



| Field | Description |
| --- | --- |
| `blueGreen` _[BlueGreenStrategy](#bluegreenstrategy)_ | BlueGreen holds the options specific for Blue Green Deployments. |


_Appears in:_
- [Rollout](#rollout)

#### Scaling


Scaling defines the scaling options for the deployment.



| Field | Description |
| --- | --- |
| `horizontal` _[HorizontalScaling](#horizontalscaling)_ | HorizontalScaling defines horizontal scaling options for the deployment. |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)
- [DeploymentOptions](#deploymentoptions)

#### ServiceOptions


ServiceOptions is used to includes options to customize the ingress service,
such as the annotations.



| Field | Description |
| --- | --- |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [DataPlaneServiceOptions](#dataplaneserviceoptions)
- [GatewayConfigServiceOptions](#gatewayconfigserviceoptions)

#### WatchNamespaces


WatchNamespaces defines the namespaces to watch for resources



| Field | Description |
| --- | --- |
| `type` _[WatchNamespacesType](#watchnamespacestype)_ | Type indicates the type of namespace watching to be done. By default, all namespaces are watched. |
| `list` _string array_ | List is a list of namespaces to watch for resources. Only used when Type is set to List. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneSpec](#controlplanespec)

#### WatchNamespacesType
_Underlying type:_ `string`

WatchNamespacesType indicates the type of namespace watching to be done.





_Appears in:_
- [WatchNamespaces](#watchnamespaces)


## gateway-operator.konghq.com/v2alpha1

Package v2alpha1 contains API Schema definitions for the gateway-operator.konghq.com v2alpha1 API group.

Package v2alpha1 contains API Schema definitions for the gateway-operator.konghq.com v2alpha1 API group

- [ControlPlane](#controlplane)
### ControlPlane


ControlPlane is the Schema for the controlplanes API

<!-- control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v2alpha1`
| `kind` _string_ | `ControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[ControlPlaneSpec](#controlplanespec)_ | Spec is the specification of the ControlPlane resource. |



### Types

In this section you will find types that the CRDs rely on.
#### ControlPlaneAdminAPI


ControlPlaneAdminAPI defines the configuration for the DataPlane Kong Admin API.



| Field | Description |
| --- | --- |
| `workspace` _string_ | Workspace indicates the Kong Workspace to use for the ControlPlane. If left empty then no Kong workspace will be used. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneController


ControlPlaneController defines a controller state for the ControlPlane.
It overrides the default behavior as defined in the deployed operator version.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the controller. |
| `state` _[ControllerState](#controllerstate)_ | State indicates whether the feature gate is enabled or disabled. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneStatus](#controlplanestatus)

#### ControlPlaneDataPlaneTarget


ControlPlaneDataPlaneTarget defines the target for the DataPlane that the ControlPlane
is responsible for configuring.



| Field | Description |
| --- | --- |
| `type` _[ControlPlaneDataPlaneTargetType](#controlplanedataplanetargettype)_ | Type indicates the type of the DataPlane target. |
| `external` _[ControlPlaneDataPlaneTargetExternal](#controlplanedataplanetargetexternal)_ | External is the External of the DataPlane target. This is used for configuring externally managed DataPlanes like those installed independently with Helm. |
| `ref` _[ControlPlaneDataPlaneTargetRef](#controlplanedataplanetargetref)_ | Ref is the name of the DataPlane to configure. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneDataPlaneTargetExternal


ControlPlaneDataPlaneTargetExternal defines the configuration for an external DataPlane
that the ControlPlane is responsible for configuring.



| Field | Description |
| --- | --- |
| `url` _string_ | URL is the URL of the external DataPlane to configure. |


_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneDataPlaneTargetRef


ControlPlaneDataPlaneTargetRef defines the reference to a DataPlane resource
that the ControlPlane is responsible for configuring.



| Field | Description |
| --- | --- |
| `name` _string_ | Ref is the name of the DataPlane to configure. |


_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneDataPlaneTargetType
_Underlying type:_ `string`

ControlPlaneDataPlaneTargetType defines the type of the DataPlane target
that the ControlPlane is responsible for configuring.





_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneFeatureGate


ControlPlaneFeatureGate defines a feature gate state for the ControlPlane.
It overrides the default behavior as defined in the deployed operator version.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the feature gate. |
| `state` _[FeatureGateState](#featuregatestate)_ | State indicates whether the feature gate is enabled or disabled. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneStatus](#controlplanestatus)

#### ControlPlaneOptions


ControlPlaneOptions indicates the specific information needed to
deploy and connect a ControlPlane to a DataPlane object.



| Field | Description |
| --- | --- |
| `dataplane` _[ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)_ | DataPlane designates the target data plane to configure.<br /><br /> It can be either a URL to an externally managed DataPlane (e.g. installed independently with Helm) or a name of a DataPlane resource that is managed by the operator. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `featureGates` _[ControlPlaneFeatureGate](#controlplanefeaturegate) array_ | FeatureGates is a list of feature gates that are enabled for this ControlPlane. |
| `controllers` _[ControlPlaneController](#controlplanecontroller) array_ | Controllers defines the controllers that are enabled for this ControlPlane. |
| `adminAPI` _[ControlPlaneAdminAPI](#controlplaneadminapi)_ | AdminAPI defines the configuration for the Kong Admin API. |


_Appears in:_
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneSpec


ControlPlaneSpec defines the desired state of ControlPlane



| Field | Description |
| --- | --- |
| `dataplane` _[ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)_ | DataPlane designates the target data plane to configure.<br /><br /> It can be either a URL to an externally managed DataPlane (e.g. installed independently with Helm) or a name of a DataPlane resource that is managed by the operator. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `featureGates` _[ControlPlaneFeatureGate](#controlplanefeaturegate) array_ | FeatureGates is a list of feature gates that are enabled for this ControlPlane. |
| `controllers` _[ControlPlaneController](#controlplanecontroller) array_ | Controllers defines the controllers that are enabled for this ControlPlane. |
| `adminAPI` _[ControlPlaneAdminAPI](#controlplaneadminapi)_ | AdminAPI defines the configuration for the Kong Admin API. |
| `ingressClass` _string_ | IngressClass enables support for the older Ingress resource and indicates which Ingress resources this ControlPlane should be responsible for.<br /><br /> If omitted, Ingress resources will not be supported by the ControlPlane. |


_Appears in:_
- [ControlPlane](#controlplane)



#### ControllerState
_Underlying type:_ `string`

ControllerState defines the state of a feature gate.





_Appears in:_
- [ControlPlaneController](#controlplanecontroller)

#### FeatureGateState
_Underlying type:_ `string`

FeatureGateState defines the state of a feature gate.





_Appears in:_
- [ControlPlaneFeatureGate](#controlplanefeaturegate)


## incubator.ingress-controller.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the incubator.ingress-controller.konghq.com v1alpha1 API group.

- [KongServiceFacade](#kongservicefacade)
### KongServiceFacade


KongServiceFacade allows creating separate Kong Services for a single Kubernetes
Service. It can be used as Kubernetes Ingress' backend (via its path's `backend.resource`
field). It's designed to enable creating two "virtual" Services in Kong that will point
to the same Kubernetes Service, but will have different configuration (e.g. different
set of plugins, different load balancing algorithm, etc.).<br /><br />
KongServiceFacade requires `kubernetes.io/ingress.class` annotation with a value
matching the ingressClass of the Kong Ingress Controller (`kong` by default) to be reconciled.

<!-- kong_service_facade description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `incubator.ingress-controller.konghq.com/v1alpha1`
| `kind` _string_ | `KongServiceFacade`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongServiceFacadeSpec](#kongservicefacadespec)_ |  |



### Types

In this section you will find types that the CRDs rely on.
#### KongServiceFacadeBackend


KongServiceFacadeBackend is a reference to a Kubernetes Service
that is used as a backend for a Kong Service Facade.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the referenced Kubernetes Service. |
| `port` _integer_ | Port is the port of the referenced Kubernetes Service. |


_Appears in:_
- [KongServiceFacadeSpec](#kongservicefacadespec)

#### KongServiceFacadeSpec


KongServiceFacadeSpec defines the desired state of KongServiceFacade.



| Field | Description |
| --- | --- |
| `backendRef` _[KongServiceFacadeBackend](#kongservicefacadebackend)_ | Backend is a reference to a Kubernetes Service that is used as a backend for this Kong Service Facade. |


_Appears in:_
- [KongServiceFacade](#kongservicefacade)




## konnect.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the konnect.konghq.com v1alpha1 API group.

- [KonnectAPIAuthConfiguration](#konnectapiauthconfiguration)
- [KonnectCloudGatewayDataPlaneGroupConfiguration](#konnectcloudgatewaydataplanegroupconfiguration)
- [KonnectCloudGatewayNetwork](#konnectcloudgatewaynetwork)
- [KonnectCloudGatewayTransitGateway](#konnectcloudgatewaytransitgateway)
- [KonnectExtension](#konnectextension)
- [KonnectGatewayControlPlane](#konnectgatewaycontrolplane)
### KonnectAPIAuthConfiguration


KonnectAPIAuthConfiguration is the Schema for the Konnect configuration type.

<!-- konnect_api_auth_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectAPIAuthConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectAPIAuthConfigurationSpec](#konnectapiauthconfigurationspec)_ | Spec is the specification of the KonnectAPIAuthConfiguration resource. |



### KonnectCloudGatewayDataPlaneGroupConfiguration


KonnectCloudGatewayDataPlaneGroupConfiguration is the Schema for the Konnect Network API.

<!-- konnect_cloud_gateway_data_plane_group_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayDataPlaneGroupConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayDataPlaneGroupConfigurationSpec](#konnectcloudgatewaydataplanegroupconfigurationspec)_ | Spec defines the desired state of KonnectCloudGatewayDataPlaneGroupConfiguration. |



### KonnectCloudGatewayNetwork


KonnectCloudGatewayNetwork is the Schema for the Konnect Network API.

<!-- konnect_cloud_gateway_network description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayNetwork`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayNetworkSpec](#konnectcloudgatewaynetworkspec)_ | Spec defines the desired state of KonnectCloudGatewayNetwork. |



### KonnectCloudGatewayTransitGateway


KonnectCloudGatewayTransitGateway is the Schema for the Konnect Transit Gateway API.

<!-- konnect_cloud_gateway_transit_gateway description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayTransitGateway`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)_ | Spec defines the desired state of KonnectCloudGatewayTransitGateway. |



### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
deployment(s) spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### KonnectGatewayControlPlane


KonnectGatewayControlPlane is the Schema for the KonnectGatewayControlplanes API.

<!-- konnect_gateway_control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectGatewayControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)_ | Spec defines the desired state of KonnectGatewayControlPlane. |



### Types

In this section you will find types that the CRDs rely on.


#### AWSTransitGateway


AWSTransitGateway is the configuration of an AWS transit gateway.



| Field | Description |
| --- | --- |
| `name` _string_ | Human-readable name of the transit gateway. |
| `dns_config` _[TransitGatewayDNSConfig](#transitgatewaydnsconfig) array_ | List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway attachment. |
| `cidr_blocks` _string array_ | CIDR blocks for constructing a route table for the transit gateway, when attaching to the owning network. |
| `attachment_config` _[AwsTransitGatewayAttachmentConfig](#awstransitgatewayattachmentconfig)_ | configuration to attach to AWS transit gateway on the AWS side. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)

#### AwsTransitGatewayAttachmentConfig


AwsTransitGatewayAttachmentConfig is the configuration to attach to a AWS transit gateway.



| Field | Description |
| --- | --- |
| `transit_gateway_id` _string_ | TransitGatewayID is the AWS transit gateway ID to create attachment to. |
| `ram_share_arn` _string_ | RAMShareArn is the resource share ARN to verify request to create transit gateway attachment. |


_Appears in:_
- [AWSTransitGateway](#awstransitgateway)

#### AzureTransitGateway


AzureTransitGateway is the configuration of an Azure transit gateway.



| Field | Description |
| --- | --- |
| `name` _string_ | Human-readable name of the transit gateway. |
| `dns_config` _[TransitGatewayDNSConfig](#transitgatewaydnsconfig) array_ | List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway attachment. |
| `attachment_config` _[AzureVNETPeeringAttachmentConfig](#azurevnetpeeringattachmentconfig)_ | configuration to attach to Azure VNET peering gateway. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)

#### AzureVNETPeeringAttachmentConfig


AzureVNETPeeringAttachmentConfig is the configuration to attach to a Azure VNET peering gateway.



| Field | Description |
| --- | --- |
| `tenant_id` _string_ | TenantID is the tenant ID for the Azure VNET Peering attachment. |
| `subscription_id` _string_ | SubscriptionID is the subscription ID for the Azure VNET Peering attachment. |
| `resource_group_name` _string_ | ResourceGroupName is the resource group name for the Azure VNET Peering attachment. |
| `vnet_name` _string_ | VnetName is the VNET Name for the Azure VNET Peering attachment. |


_Appears in:_
- [AzureTransitGateway](#azuretransitgateway)

#### CertificateSecret


CertificateSecret contains the information to access the client certificate.



| Field | Description |
| --- | --- |
| `provisioning` _[ProvisioningMethod](#provisioningmethod)_ | Provisioning is the method used to provision the certificate. It can be either Manual or Automatic. In case manual provisioning is used, the certificate must be provided by the user. In case automatic provisioning is used, the certificate will be automatically generated by the system. |
| `secretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionClientAuth](#konnectextensionclientauth)

#### ConfigurationDataPlaneGroupAutoscale


ConfigurationDataPlaneGroupAutoscale specifies the autoscale configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `static` _[ConfigurationDataPlaneGroupAutoscaleStatic](#configurationdataplanegroupautoscalestatic)_ | Static specifies the static configuration for the data-plane group. |
| `autopilot` _[ConfigurationDataPlaneGroupAutoscaleAutopilot](#configurationdataplanegroupautoscaleautopilot)_ | Autopilot specifies the autoscale configuration for the data-plane group. |
| `type` _[ConfigurationDataPlaneGroupAutoscaleType](#configurationdataplanegroupautoscaletype)_ | Type of autoscaling to use. |


_Appears in:_
- [KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup)

#### ConfigurationDataPlaneGroupAutoscaleAutopilot


ConfigurationDataPlaneGroupAutoscaleAutopilot specifies the autoscale configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `base_rps` _integer_ | Base number of requests per second that the deployment target should support. |
| `max_rps` _integer_ | Max number of requests per second that the deployment target should support. If not set, this defaults to 10x base_rps. |


_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupAutoscaleStatic


ConfigurationDataPlaneGroupAutoscaleStatic specifies the static configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `instance_type` _[InstanceTypeName](#instancetypename)_ | Instance type name to indicate capacity. Currently supported values are small, medium, large but this list might be expanded in the future. For all the allowed values, please refer to the Konnect API documentation at https://docs.konghq.com/konnect/api/cloud-gateways/latest/#/Data-Plane%20Group%20Configurations/create-configuration. |
| `requested_instances` _integer_ | Number of data-planes the deployment target will contain. |


_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupAutoscaleType
_Underlying type:_ `string`

ConfigurationDataPlaneGroupAutoscaleType is the type of autoscale configuration for the data-plane group.





_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupEnvironmentField


ConfigurationDataPlaneGroupEnvironmentField specifies an environment variable field for the data-plane group.



| Field | Description |
| --- | --- |
| `name` _string_ | Name of the environment variable field to set for the data-plane group. Must be prefixed by KONG_. |
| `value` _string_ | Value assigned to the environment variable field for the data-plane group. |


_Appears in:_
- [KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup)

#### CreateControlPlaneRequest


CreateControlPlaneRequest - The request schema for the create control plane request.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of the control plane. |
| `description` _string_ | The description of the control plane in Konnect. |
| `cluster_type` _[CreateControlPlaneRequestClusterType](#createcontrolplanerequestclustertype)_ | The ClusterType value of the cluster associated with the Control Plane. |
| `auth_type` _[AuthType](#authtype)_ | The auth type value of the cluster associated with the Runtime Group. |
| `cloud_gateway` _boolean_ | Whether this control-plane can be used for cloud-gateways. |
| `proxy_urls` _ProxyURL array_ | Array of proxy URLs associated with reaching the data-planes connected to a control-plane. |
| `labels` _object (keys:string, values:string)_ | Labels store metadata of an entity that can be used for filtering an entity list or for searching across entity types.<br /><br /> Keys must be of length 1-63 characters, and cannot start with "kong", "konnect", "mesh", "kic", or "_". |


_Appears in:_
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### DataPlaneClientAuthStatus


DataPlaneClientAuthStatus contains the status information related to the ClientAuth configuration.



| Field | Description |
| --- | --- |
| `certificateSecretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### DataPlaneLabelValue
_Underlying type:_ `string`

DataPlaneLabelValue is the type that defines the value of a label that will be applied to the Konnect DataPlane.





_Appears in:_
- [KonnectExtensionDataPlane](#konnectextensiondataplane)

#### KonnectAPIAuthConfigurationRef


KonnectAPIAuthConfigurationRef is a reference to a KonnectAPIAuthConfiguration resource.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the KonnectAPIAuthConfiguration resource. |


_Appears in:_
- [KonnectConfiguration](#konnectconfiguration)

#### KonnectAPIAuthConfigurationSpec


KonnectAPIAuthConfigurationSpec is the specification of the KonnectAPIAuthConfiguration resource.



| Field | Description |
| --- | --- |
| `type` _[KonnectAPIAuthType](#konnectapiauthtype)_ |  |
| `token` _string_ | Token is the Konnect token used to authenticate with the Konnect API. |
| `secretRef` _[SecretReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#secretreference-v1-core)_ | SecretRef is a reference to a Kubernetes Secret containing the Konnect token. This secret is required to have the konghq.com/credential label set to "konnect". |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server. It can be either a full URL with an HTTPs scheme or just a hostname. Please refer to https://docs.konghq.com/konnect/network/ for the list of supported hostnames. |


_Appears in:_
- [KonnectAPIAuthConfiguration](#konnectapiauthconfiguration)



#### KonnectAPIAuthType
_Underlying type:_ `string`

KonnectAPIAuthType is the type of authentication used to authenticate with the Konnect API.





_Appears in:_
- [KonnectAPIAuthConfigurationSpec](#konnectapiauthconfigurationspec)

#### KonnectCloudGatewayDataPlaneGroupConfigurationSpec


KonnectCloudGatewayDataPlaneGroupConfigurationSpec defines the desired state of KonnectCloudGatewayDataPlaneGroupConfiguration.



| Field | Description |
| --- | --- |
| `version` _string_ | Version specifies the desired Kong Gateway version. |
| `dataplane_groups` _[KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup) array_ | DataplaneGroups is a list of desired data-plane groups that describe where to deploy instances, along with how many instances. |
| `api_access` _[APIAccess](#apiaccess)_ | APIAccess is the desired type of API access for data-plane groups. |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane which DataPlanes from this configuration will connect to. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfiguration](#konnectcloudgatewaydataplanegroupconfiguration)



#### KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup


KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup defines the observed state of a deployed data-plane group.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the ID of the deployed data-plane group. |
| `cloud_gateway_network_id` _string_ | CloudGatewayNetworkID is the ID of the cloud gateway network. |
| `provider` _[ProviderName](#providername)_ | Name of cloud provider. |
| `region` _string_ | Region ID for cloud provider region. |
| `private_ip_addresses` _string array_ | PrivateIPAddresses is a list of private IP addresses of the internal load balancer that proxies traffic to this data-plane group. |
| `egress_ip_addresses` _string array_ | EgressIPAddresses is a list of egress IP addresses for the network that this data-plane group runs on. |
| `state` _string_ | State is the current state of the data plane group. Can be e.g. initializing, ready, terminating. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)

#### KonnectCloudGatewayNetworkSpec


KonnectCloudGatewayNetworkSpec defines the desired state of KonnectCloudGatewayNetwork.



| Field | Description |
| --- | --- |
| `name` _string_ | Specifies the name of the network on Konnect. |
| `cloud_gateway_provider_account_id` _string_ | Specifies the provider Account ID. |
| `region` _string_ | Region ID for cloud provider region. |
| `availability_zones` _string array_ | List of availability zones that the network is attached to. |
| `cidr_block` _string_ | CIDR block configuration for the network. |
| `state` _[NetworkCreateState](#networkcreatestate)_ | Initial state for creating a network. |
| `konnect` _[KonnectConfiguration](#konnectconfiguration)_ |  |


_Appears in:_
- [KonnectCloudGatewayNetwork](#konnectcloudgatewaynetwork)



#### KonnectCloudGatewayTransitGatewaySpec


KonnectCloudGatewayTransitGatewaySpec defines the desired state of KonnectCloudGatewayTransitGateway.



| Field | Description |
| --- | --- |
| `networkRef` _[ObjectRef](#objectref)_ | NetworkRef is the schema for the NetworkRef type. |
| `type` _[TransitGatewayType](#transitgatewaytype)_ | Type is the type of the Konnect transit gateway. |
| `awsTransitGateway` _[AWSTransitGateway](#awstransitgateway)_ | AWSTransitGateway is the configuration of an AWS transit gateway. Used when type is "AWS Transit Gateway". |
| `azureTransitGateway` _[AzureTransitGateway](#azuretransitgateway)_ | AzureTransitGateway is the configuration of an Azure transit gateway. Used when type is "Azure Transit Gateway". |


_Appears in:_
- [KonnectCloudGatewayTransitGateway](#konnectcloudgatewaytransitgateway)



#### KonnectConfiguration


KonnectConfiguration is the Schema for the KonnectConfiguration API.



| Field | Description |
| --- | --- |
| `authRef` _[KonnectAPIAuthConfigurationRef](#konnectapiauthconfigurationref)_ | APIAuthConfigurationRef is the reference to the API Auth Configuration that should be used for this Konnect Configuration. |


_Appears in:_
- [KonnectCloudGatewayNetworkSpec](#konnectcloudgatewaynetworkspec)
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### KonnectConfigurationDataPlaneGroup


KonnectConfigurationDataPlaneGroup is the schema for the KonnectConfiguration type.



| Field | Description |
| --- | --- |
| `provider` _[ProviderName](#providername)_ | Name of cloud provider. |
| `region` _string_ | Region for cloud provider region. |
| `networkRef` _[ObjectRef](#objectref)_ | NetworkRef is the reference to the network that this data-plane group will be deployed on.<br /><br /> Cross namespace references are not supported for networkRef of type namespacedRef. This will be enforced in the future but currently (due to limitation in CEL validation in Kubernetes 1.31 and older) it is not. |
| `autoscale` _[ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)_ | Autoscale configuration for the data-plane group. |
| `environment` _[ConfigurationDataPlaneGroupEnvironmentField](#configurationdataplanegroupenvironmentfield) array_ | Array of environment variables to set for a data-plane group. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationSpec](#konnectcloudgatewaydataplanegroupconfigurationspec)

#### KonnectEndpoints


KonnectEndpoints defines the Konnect endpoints for the control plane.



| Field | Description |
| --- | --- |
| `telemetry` _string_ | TelemetryEndpoint is the endpoint for telemetry. |
| `controlPlane` _string_ | ControlPlaneEndpoint is the endpoint for the control plane. |


_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)
- [KonnectGatewayControlPlaneStatus](#konnectgatewaycontrolplanestatus)

#### KonnectEntityStatus


KonnectEntityStatus represents the status of a Konnect entity.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)
- [KonnectCloudGatewayNetworkStatus](#konnectcloudgatewaynetworkstatus)
- [KonnectCloudGatewayTransitGatewayStatus](#konnectcloudgatewaytransitgatewaystatus)
- [KonnectEntityStatusWithControlPlaneAndCertificateRefs](#konnectentitystatuswithcontrolplaneandcertificaterefs)
- [KonnectEntityStatusWithControlPlaneAndConsumerRefs](#konnectentitystatuswithcontrolplaneandconsumerrefs)
- [KonnectEntityStatusWithControlPlaneAndKeySetRef](#konnectentitystatuswithcontrolplaneandkeysetref)
- [KonnectEntityStatusWithControlPlaneAndServiceRefs](#konnectentitystatuswithcontrolplaneandservicerefs)
- [KonnectEntityStatusWithControlPlaneAndUpstreamRefs](#konnectentitystatuswithcontrolplaneandupstreamrefs)
- [KonnectEntityStatusWithControlPlaneRef](#konnectentitystatuswithcontrolplaneref)
- [KonnectEntityStatusWithNetworkRef](#konnectentitystatuswithnetworkref)
- [KonnectGatewayControlPlaneStatus](#konnectgatewaycontrolplanestatus)

#### KonnectEntityStatusWithControlPlaneAndCertificateRefs


KonnectEntityStatusWithControlPlaneAndCertificateRefs represents the status of a Konnect entity with references to a ControlPlane and a Certificate.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this entity is associated with. |
| `certificateID` _string_ | CertificateID is the Konnect ID of the Certificate this entity is associated with. |


_Appears in:_
- [KongSNIStatus](#kongsnistatus)

#### KonnectEntityStatusWithControlPlaneAndConsumerRefs


KonnectEntityStatusWithControlPlaneAndConsumerRefs represents the status of a Konnect entity with references to a ControlPlane and a Consumer.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this Route is associated with. |
| `consumerID` _string_ | ConsumerID is the Konnect ID of the Consumer this entity is associated with. |


_Appears in:_
- [KongCredentialACLStatus](#kongcredentialaclstatus)
- [KongCredentialAPIKeyStatus](#kongcredentialapikeystatus)
- [KongCredentialBasicAuthStatus](#kongcredentialbasicauthstatus)
- [KongCredentialHMACStatus](#kongcredentialhmacstatus)
- [KongCredentialJWTStatus](#kongcredentialjwtstatus)

#### KonnectEntityStatusWithControlPlaneAndKeySetRef


KonnectEntityStatusWithControlPlaneAndKeySetRef represents the status of a Konnect entity with references to a ControlPlane and a KeySet.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this entity is associated with. |
| `keySetID` _string_ | KeySetID is the Konnect ID of the KeySet this entity is associated with. |


_Appears in:_
- [KongKeyStatus](#kongkeystatus)

#### KonnectEntityStatusWithControlPlaneAndServiceRefs


KonnectEntityStatusWithControlPlaneAndServiceRefs represents the status of a Konnect entity with references to a ControlPlane and a Service.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this entity is associated with. |
| `serviceID` _string_ | ServiceID is the Konnect ID of the Service this entity is associated with. |


_Appears in:_
- [KongRouteStatus](#kongroutestatus)

#### KonnectEntityStatusWithControlPlaneAndUpstreamRefs


KonnectEntityStatusWithControlPlaneAndUpstreamRefs represents the status of a Konnect entity with references to a ControlPlane and an Upstream.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this entity is associated with. |
| `upstreamID` _string_ | UpstreamID is the Konnect ID of the Upstream this entity is associated with. |


_Appears in:_
- [KongTargetStatus](#kongtargetstatus)

#### KonnectEntityStatusWithControlPlaneRef


KonnectEntityStatusWithControlPlaneRef represents the status of a Konnect entity with a reference to a ControlPlane.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this Route is associated with. |


_Appears in:_
- [KongCACertificateStatus](#kongcacertificatestatus)
- [KongCertificateStatus](#kongcertificatestatus)
- [KongConsumerGroupStatus](#kongconsumergroupstatus)
- [KongConsumerStatus](#kongconsumerstatus)
- [KongDataPlaneClientCertificateStatus](#kongdataplaneclientcertificatestatus)
- [KongKeySetStatus](#kongkeysetstatus)
- [KongPluginBindingStatus](#kongpluginbindingstatus)
- [KongServiceStatus](#kongservicestatus)
- [KongUpstreamStatus](#kongupstreamstatus)
- [KongVaultStatus](#kongvaultstatus)
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)

#### KonnectEntityStatusWithNetworkRef


KonnectEntityStatusWithNetworkRef represents the status of a Konnect entity with reference to a Konnect cloud gateway network.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `networkID` _string_ | NetworkID is the Konnect ID of the Konnect cloud gateway network this entity is associated with. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewayStatus](#konnectcloudgatewaytransitgatewaystatus)

#### KonnectExtensionClientAuth


KonnectExtensionClientAuth contains the configuration for the client authentication for the DataPlane.
At the moment authentication is only supported through client certificate, but it might be extended in the future,
with e.g., token-based authentication.



| Field | Description |
| --- | --- |
| `certificateSecret` _[CertificateSecret](#certificatesecret)_ | CertificateSecret contains the information to access the client certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionClusterType
_Underlying type:_ `string`

KonnectExtensionClusterType is the type of the Konnect Control Plane.





_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionControlPlane


KonnectExtensionControlPlane is the configuration for the Konnect Control Plane.



| Field | Description |
| --- | --- |
| `ref` _[ControlPlaneRef](#controlplaneref)_ | Ref is a reference to a Konnect ControlPlane this KonnectExtension is associated with. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionControlPlaneStatus


KonnectExtensionControlPlaneStatus contains the Konnect Control Plane status information.



| Field | Description |
| --- | --- |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this KonnectExtension is associated with. |
| `clusterType` _[KonnectExtensionClusterType](#konnectextensionclustertype)_ | ClusterType is the type of the Konnect Control Plane. |
| `endpoints` _[KonnectEndpoints](#konnectendpoints)_ | Endpoints defines the Konnect endpoints for the control plane. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### KonnectExtensionDataPlane


KonnectExtensionDataPlane is the configuration for the Konnect DataPlane.



| Field | Description |
| --- | --- |
| `labels` _object (keys:string, values:[DataPlaneLabelValue](#dataplanelabelvalue))_ | Labels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionKonnectSpec


KonnectExtensionKonnectSpec holds the konnect-related configuration.



| Field | Description |
| --- | --- |
| `controlPlane` _[KonnectExtensionControlPlane](#konnectextensioncontrolplane)_ | ControlPlane is the configuration for the Konnect Control Plane. |
| `dataPlane` _[KonnectExtensionDataPlane](#konnectextensiondataplane)_ | DataPlane is the configuration for the Konnect DataPlane. |
| `configuration` _[KonnectConfiguration](#konnectconfiguration)_ | Configuration holds the information needed to set up the Konnect Configuration. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `konnect` _[KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)_ | Konnect holds the konnect-related configuration |
| `clientAuth` _[KonnectExtensionClientAuth](#konnectextensionclientauth)_ | ClientAuth is the configuration for the client certificate authentication. In case the ControlPlaneRef is of type KonnectID, it is required to set up the connection with the Konnect Platform. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### KonnectGatewayControlPlaneSpec


KonnectGatewayControlPlaneSpec defines the desired state of KonnectGatewayControlPlane.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of the control plane. |
| `description` _string_ | The description of the control plane in Konnect. |
| `cluster_type` _[CreateControlPlaneRequestClusterType](#createcontrolplanerequestclustertype)_ | The ClusterType value of the cluster associated with the Control Plane. |
| `auth_type` _[AuthType](#authtype)_ | The auth type value of the cluster associated with the Runtime Group. |
| `cloud_gateway` _boolean_ | Whether this control-plane can be used for cloud-gateways. |
| `proxy_urls` _ProxyURL array_ | Array of proxy URLs associated with reaching the data-planes connected to a control-plane. |
| `labels` _object (keys:string, values:string)_ | Labels store metadata of an entity that can be used for filtering an entity list or for searching across entity types.<br /><br /> Keys must be of length 1-63 characters, and cannot start with "kong", "konnect", "mesh", "kic", or "_". |
| `mirror` _[MirrorSpec](#mirrorspec)_ | Mirror is the Konnect Mirror configuration. It is only applicable for ControlPlanes that are created as Mirrors. |
| `source` _[EntitySource](#entitysource)_ | Source represents the source type of the Konnect entity. |
| `members` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core) array_ | Members is a list of references to the KonnectGatewayControlPlaneMembers that are part of this control plane group. Only applicable for ControlPlanes that are created as groups. |
| `konnect` _[KonnectConfiguration](#konnectconfiguration)_ | KonnectConfiguration contains the Konnect configuration for the control plane. |


_Appears in:_
- [KonnectGatewayControlPlane](#konnectgatewaycontrolplane)



#### KonnectTransitGatewayAPISpec


KonnectTransitGatewayAPISpec specifies a transit gateway on the Konnect side.
The type and all the types it referenced are mostly copied github.com/Kong/sdk-konnect-go/models/components.CreateTransitGatewayRequest.



| Field | Description |
| --- | --- |
| `type` _[TransitGatewayType](#transitgatewaytype)_ | Type is the type of the Konnect transit gateway. |
| `awsTransitGateway` _[AWSTransitGateway](#awstransitgateway)_ | AWSTransitGateway is the configuration of an AWS transit gateway. Used when type is "AWS Transit Gateway". |
| `azureTransitGateway` _[AzureTransitGateway](#azuretransitgateway)_ | AzureTransitGateway is the configuration of an Azure transit gateway. Used when type is "Azure Transit Gateway". |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)

#### MirrorKonnect


MirrorKonnect contains the Konnect Mirror configuration.



| Field | Description |
| --- | --- |
| `id` _[KonnectIDType](#konnectidtype)_ | ID is the ID of the Konnect entity. It can be set only in case the ControlPlane type is Mirror. |


_Appears in:_
- [MirrorSpec](#mirrorspec)

#### MirrorSpec


MirrorSpec contains the Konnect Mirror configuration.



| Field | Description |
| --- | --- |
| `konnect` _[MirrorKonnect](#mirrorkonnect)_ | Konnect contains the KonnectID of the KonnectGatewayControlPlane that is mirrored. |


_Appears in:_
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### ProvisioningMethod
_Underlying type:_ `string`

ProvisioningMethod is the type of the provisioning methods available to provision the certificate.





_Appears in:_
- [CertificateSecret](#certificatesecret)

#### SecretRef


SecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [CertificateSecret](#certificatesecret)
- [DataPlaneClientAuthStatus](#dataplaneclientauthstatus)

#### TransitGatewayDNSConfig


TransitGatewayDNSConfig is the DNS configuration of a tansit gateway.



| Field | Description |
| --- | --- |
| `remote_dns_server_ip_addresses` _string array_ | RemoteDNSServerIPAddresses is the list of remote DNS server IP Addresses to connect to for resolving internal DNS via a transit gateway. |
| `domain_proxy_list` _string array_ | DomainProxyList is the list of internal domain names to proxy for DNS resolution from the listed remote DNS server IP addresses, for a transit gateway. |


_Appears in:_
- [AWSTransitGateway](#awstransitgateway)
- [AzureTransitGateway](#azuretransitgateway)

#### TransitGatewayType
_Underlying type:_ `string`

TransitGatewayType defines the type of Konnect transit gateway.





_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)


## konnect.konghq.com/v1alpha2

Package v1alpha2 contains API Schema definitions for the konnect.konghq.com v1alpha2 API group.

- [KonnectExtension](#konnectextension)
### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
deployment(s) spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha2`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### Types

In this section you will find types that the CRDs rely on.
#### CertificateSecret


CertificateSecret contains the information to access the client certificate.



| Field | Description |
| --- | --- |
| `provisioning` _[ProvisioningMethod](#provisioningmethod)_ | Provisioning is the method used to provision the certificate. It can be either Manual or Automatic. In case manual provisioning is used, the certificate must be provided by the user. In case automatic provisioning is used, the certificate will be automatically generated by the system. |
| `secretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionClientAuth](#konnectextensionclientauth)

#### DataPlaneClientAuthStatus


DataPlaneClientAuthStatus contains the status information related to the ClientAuth configuration.



| Field | Description |
| --- | --- |
| `certificateSecretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### DataPlaneLabelValue
_Underlying type:_ `string`

DataPlaneLabelValue is the type that defines the value of a label that will be applied to the Konnect DataPlane.





_Appears in:_
- [KonnectExtensionDataPlane](#konnectextensiondataplane)

#### KonnectEndpoints


KonnectEndpoints defines the Konnect endpoints for the control plane.



| Field | Description |
| --- | --- |
| `telemetry` _string_ | TelemetryEndpoint is the endpoint for telemetry. |
| `controlPlane` _string_ | ControlPlaneEndpoint is the endpoint for the control plane. |


_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionClientAuth


KonnectExtensionClientAuth contains the configuration for the client authentication for the DataPlane.
At the moment authentication is only supported through client certificate, but it might be extended in the future,
with e.g., token-based authentication.



| Field | Description |
| --- | --- |
| `certificateSecret` _[CertificateSecret](#certificatesecret)_ | CertificateSecret contains the information to access the client certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionClusterType
_Underlying type:_ `string`

KonnectExtensionClusterType is the type of the Konnect Control Plane.





_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionControlPlane


KonnectExtensionControlPlane is the configuration for the Konnect Control Plane.



| Field | Description |
| --- | --- |
| `ref` _[KonnectExtensionControlPlaneRef](#konnectextensioncontrolplaneref)_ | Ref is a reference to a Konnect ControlPlane this KonnectExtension is associated with. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionControlPlaneStatus


KonnectExtensionControlPlaneStatus contains the Konnect Control Plane status information.



| Field | Description |
| --- | --- |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this KonnectExtension is associated with. |
| `clusterType` _[KonnectExtensionClusterType](#konnectextensionclustertype)_ | ClusterType is the type of the Konnect Control Plane. |
| `endpoints` _[KonnectEndpoints](#konnectendpoints)_ | Endpoints defines the Konnect endpoints for the control plane. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### KonnectExtensionDataPlane


KonnectExtensionDataPlane is the configuration for the Konnect DataPlane.



| Field | Description |
| --- | --- |
| `labels` _object (keys:string, values:[DataPlaneLabelValue](#dataplanelabelvalue))_ | Labels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionKonnectSpec


KonnectExtensionKonnectSpec holds the konnect-related configuration.



| Field | Description |
| --- | --- |
| `controlPlane` _[KonnectExtensionControlPlane](#konnectextensioncontrolplane)_ | ControlPlane is the configuration for the Konnect Control Plane. |
| `dataPlane` _[KonnectExtensionDataPlane](#konnectextensiondataplane)_ | DataPlane is the configuration for the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `konnect` _[KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)_ | Konnect holds the konnect-related configuration |
| `clientAuth` _[KonnectExtensionClientAuth](#konnectextensionclientauth)_ | ClientAuth is the configuration for the client certificate authentication. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### ProvisioningMethod
_Underlying type:_ `string`

ProvisioningMethod is the type of the provisioning methods available to provision the certificate.





_Appears in:_
- [CertificateSecret](#certificatesecret)

#### SecretRef


SecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [CertificateSecret](#certificatesecret)
- [DataPlaneClientAuthStatus](#dataplaneclientauthstatus)

