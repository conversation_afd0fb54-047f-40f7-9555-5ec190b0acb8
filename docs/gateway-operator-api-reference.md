<!-- This document is generated by kong/kubernetes-configuration's 'generate.docs' make target, DO NOT EDIT -->

## Packages
- [gateway-operator.konghq.com/v1alpha1](#gateway-operatorkonghqcomv1alpha1)
- [gateway-operator.konghq.com/v1beta1](#gateway-operatorkonghqcomv1beta1)
- [gateway-operator.konghq.com/v2alpha1](#gateway-operatorkonghqcomv2alpha1)


## gateway-operator.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the gateway-operator.konghq.com v1alpha1 API group.

- [AIGateway](#aigateway)
- [DataPlaneMetricsExtension](#dataplanemetricsextension)
- [KongPluginInstallation](#kongplugininstallation)
- [KonnectExtension](#konnectextension)
- [WatchNamespaceGrant](#watchnamespacegrant)
### AIGateway


AIGateway is a network Gateway enabling access and management for AI &
Machine Learning models such as Large Language Models (LLM).<br /><br />
The underlying technology for the AIGateway is the Kong Gateway configured
with a variety of plugins which provide the the AI featureset.<br /><br />
This is a list of the plugins, which are available in Kong Gateway v3.6.x+:<br /><br />
  - ai-proxy (https://github.com/kong/kong/tree/master/kong/plugins/ai-proxy)
  - ai-request-transformer (https://github.com/kong/kong/tree/master/kong/plugins/ai-request-transformer)
  - ai-response-transformers (https://github.com/kong/kong/tree/master/kong/plugins/ai-response-transformer)
  - ai-prompt-template (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-template)
  - ai-prompt-guard-plugin (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-guard)
  - ai-prompt-decorator-plugin (https://github.com/kong/kong/tree/master/kong/plugins/ai-prompt-decorator)<br /><br />
So effectively the AIGateway resource provides a bespoke Gateway resource
(which it owns and manages) with the gateway, consumers and plugin
configurations automated and configurable via Kubernetes APIs.<br /><br />
The current iteration only supports the proxy itself, but the API is being
built with room for future growth in several dimensions. For instance:<br /><br />
  - Supporting auxiliary functions (e.g. decorator, guard, templater, token-rate-limit)
  - Supporting request/response transformers
  - Supporting more than just LLMs (e.g. CCNs, GANs, e.t.c.)
  - Supporting more hosting options for LLMs (e.g. self hosted)
  - Supporting more AI cloud providers
  - Supporting more AI cloud provider features<br /><br />
The validation rules throughout are set up to ensure at least one
cloud-provider-based LLM is specified, but in the future when we have more
model types and more hosting options for those types so we may want to look
into using CEL validation to ensure that at least one model configuration is
provided. We may also want to use CEL to validate things like identifier
unique-ness, e.t.c.<br /><br />
See: https://kubernetes.io/docs/reference/using-api/cel/

<!-- ai_gateway description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `AIGateway`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[AIGatewaySpec](#aigatewayspec)_ | Spec is the desired state of the AIGateway. |



### DataPlaneMetricsExtension


DataPlaneMetricsExtension holds the configuration for the DataPlane metrics extension.
It can be attached to a ControlPlane using its spec.extensions.
When attached it will make the ControlPlane configure its DataPlane with
the specified metrics configuration.
Additionally, it will also make the operator expose DataPlane's metrics
enriched with metadata required for in-cluster Kubernetes autoscaling.

<!-- data_plane_metrics_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `DataPlaneMetricsExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)_ |  |



### KongPluginInstallation


KongPluginInstallation allows using a custom Kong Plugin distributed as a container image available in a registry.
Such a plugin can be associated with GatewayConfiguration or DataPlane to be available for particular Kong Gateway
and configured with KongPlugin CRD.

<!-- kong_plugin_installation description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `KongPluginInstallation`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KongPluginInstallationSpec](#kongplugininstallationspec)_ |  |



### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API,
and is intended to be referenced as extension by the DataPlane API.
If a DataPlane successfully refers a KonnectExtension, the DataPlane
deployment spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### WatchNamespaceGrant


WatchNamespaceGrant is a grant that allows a trusted namespace to watch
resources in the namespace this grant exists in.

<!-- watch_namespace_grant description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1alpha1`
| `kind` _string_ | `WatchNamespaceGrant`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[WatchNamespaceGrantSpec](#watchnamespacegrantspec)_ | Spec is the desired state of the WatchNamespaceGrant. |



### Types

In this section you will find types that the CRDs rely on.
#### AICloudProvider


AICloudProvider is the organization that provides API access to Large Language
Models (LLMs).



| Field | Description |
| --- | --- |
| `name` _[AICloudProviderName](#aicloudprovidername)_ | Name is the unique name of an LLM provider. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### AICloudProviderAPITokenRef


AICloudProviderAPITokenRef is an reference to another object which contains
the API token for an AI cloud provider.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the reference object. |
| `namespace` _string_ | Namespace is the namespace of the reference object.<br /><br /> If not specified, it will be assumed to be the same namespace as the object which references it. |
| `kind` _string_ | Kind is the API object kind<br /><br /> If not specified, it will be assumed to be "Secret". If a Secret is used as the Kind, the secret must contain a single key-value pair where the value is the secret API token. The key can be named anything, as long as there's only one entry, but by convention it should be "apiToken". |


_Appears in:_
- [AIGatewaySpec](#aigatewayspec)

#### AICloudProviderName
_Underlying type:_ `string`

AICloudProviderName indicates the unique name of a supported AI cloud
provider.





_Appears in:_
- [AICloudProvider](#aicloudprovider)

#### AIGatewayConsumerRef


AIGatewayConsumerRef indicates the Secret resource containing the credentials
for the Kong consumer.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the reference object. |
| `namespace` _string_ | Namespace is the namespace of the reference object. |


_Appears in:_
- [AIGatewayEndpoint](#aigatewayendpoint)

#### AIGatewayEndpoint


AIGatewayEndpoint is a network endpoint for accessing an AIGateway.



| Field | Description |
| --- | --- |
| `network` _[EndpointNetworkAccessHint](#endpointnetworkaccesshint)_ | NetworkAccessHint is a hint to the user about what kind of network access is expected for the reachability of this endpoint. |
| `url` _string_ | URL is the URL to access the endpoint from the network indicated by the NetworkAccessHint. |
| `models` _string array_ | AvailableModels is a list of the identifiers of all the AI models that are accessible from this endpoint. |
| `consumer` _[AIGatewayConsumerRef](#aigatewayconsumerref)_ | Consumer is a reference to the Secret that contains the credentials for the Kong consumer that is allowed to access this endpoint. |
| `conditions` _[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#condition-v1-meta) array_ | Conditions describe the current conditions of the AIGatewayEndpoint.<br /><br /> Known condition types are:<br /><br />   - "Provisioning"   - "EndpointReady" |


_Appears in:_
- [AIGatewayStatus](#aigatewaystatus)

#### AIGatewaySpec


AIGatewaySpec defines the desired state of an AIGateway.



| Field | Description |
| --- | --- |
| `gatewayClassName` _string_ | GatewayClassName is the name of the GatewayClass which is responsible for the AIGateway. |
| `largeLanguageModels` _[LargeLanguageModels](#largelanguagemodels)_ | LargeLanguageModels is a list of Large Language Models (LLMs) to be managed by the AI Gateway.<br /><br /> This is a required field because we only support LLMs at the moment. In future iterations we may support other model types. |
| `cloudProviderCredentials` _[AICloudProviderAPITokenRef](#aicloudproviderapitokenref)_ | CloudProviderCredentials is a reference to an object (e.g. a Kubernetes Secret) which contains the credentials needed to access the APIs of cloud providers.<br /><br /> This is the global configuration that will be used by DEFAULT for all model configurations. A secret configured this way MAY include any number of key-value pairs equal to the number of providers you have, but used this way the keys MUST be named according to their providers (e.g. "openai", "azure", "cohere", e.t.c.). For example:<br /><br />   apiVersion: v1   kind: Secret   metadata:     name: devteam-ai-cloud-providers   type: Opaque   data:     openai: *****************     azure: *****************     cohere: *****************<br /><br /> See AICloudProviderName for a list of known and valid cloud providers.<br /><br /> Note that the keys are NOT case-sensitive (e.g. "OpenAI", "openai", and "openAI" are all valid and considered the same keys) but if there are duplicates endpoints failures conditions will be emitted and endpoints will not be configured until the duplicates are resolved.<br /><br /> This is currently considered required, but in future iterations will be optional as we do things like enable configuring credentials at the model level. |


_Appears in:_
- [AIGateway](#aigateway)



#### CloudHostedLargeLanguageModel


CloudHostedLargeLanguageModel is the configuration for Large Language Models
(LLM) hosted by a known and supported AI cloud provider (e.g. OpenAI, Cohere,
Azure, e.t.c.).



| Field | Description |
| --- | --- |
| `identifier` _string_ | Identifier is the unique name which identifies the LLM. This will be used as part of the requests made to an AIGateway endpoint. For instance: if you provided the identifier "devteam-gpt-access", then you would access this model via "https://${endpoint}/devteam-gpt-access" and supply it with your consumer credentials to authenticate requests. |
| `model` _string_ | Model is the model name of the LLM (e.g. gpt-3.5-turbo, phi-2, e.t.c.).<br /><br /> If not specified, whatever the cloud provider specifies as the default model will be used. |
| `promptType` _[LLMPromptType](#llmprompttype)_ | PromptType is the type of prompt to be used for inference requests to the LLM (e.g. "chat", "completions").<br /><br /> If "chat" is specified, prompts sent by the user will be interactive, contextual and stateful. The LLM will dynamically answer questions and simulate a dialogue, while also keeping track of the conversation to provide contextually relevant responses.<br /><br /> If "completions" is specified, prompts sent by the user will be stateless and "one-shot". The LLM will provide a single response to the prompt, without any context from previous prompts.<br /><br /> If not specified, "completions" will be used as the default. |
| `defaultPrompts` _[LLMPrompt](#llmprompt) array_ | DefaultPrompts is a list of prompts that should be provided to the LLM by default. This is generally used to influence inference behavior, for instance by providing a "system" role prompt that instructs the LLM to take on a certain persona. |
| `defaultPromptParams` _[LLMPromptParams](#llmpromptparams)_ | DefaultPromptParams configures the parameters which will be sent with any and every inference request.<br /><br /> If this is set, there is currently no way to override these parameters at the individual prompt level. This is an expected feature from later releases of our AI plugins. |
| `aiCloudProvider` _[AICloudProvider](#aicloudprovider)_ | AICloudProvider defines the cloud provider that will fulfill the LLM requests for this CloudHostedLargeLanguageModel |


_Appears in:_
- [LargeLanguageModels](#largelanguagemodels)

#### ClusterCertificateSecretRef


ClusterCertificateSecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [KonnectControlPlaneAPIAuthConfiguration](#konnectcontrolplaneapiauthconfiguration)

#### DataPlaneMetricsExtensionSpec


DataPlaneMetricsExtensionSpec defines the spec for the DataPlaneMetricsExtension.



| Field | Description |
| --- | --- |
| `serviceSelector` _[ServiceSelector](#serviceselector)_ | ServiceSelector holds the service selector specifying the services for which metrics should be collected. |
| `config` _[MetricsConfig](#metricsconfig)_ | Config holds the configuration for the DataPlane metrics. |


_Appears in:_
- [DataPlaneMetricsExtension](#dataplanemetricsextension)



#### EndpointNetworkAccessHint
_Underlying type:_ `string`

EndpointNetworkAccessHint provides a human readable indication of what kind
of network access is expected for a Gateway.<br /><br />
This isn't meant to reflect knowledge of any specific network by name, which
is why it includes "hint" in the name. It's meant to be a hint to the user
such as "internet-accessible", "internal-only".





_Appears in:_
- [AIGatewayEndpoint](#aigatewayendpoint)





#### KongPluginInstallationSpec


KongPluginInstallationSpec provides the information necessary to retrieve and install a Kong custom plugin.



| Field | Description |
| --- | --- |
| `image` _string_ | The image is an OCI image URL for a packaged custom Kong plugin. |
| `imagePullSecretRef` _[SecretObjectReference](#secretobjectreference)_ | ImagePullSecretRef is a reference to a Kubernetes Secret containing credentials necessary to pull the OCI image in Image. It must follow the format in https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry. It is optional. If the image is public, omit this field. |


_Appears in:_
- [KongPluginInstallation](#kongplugininstallation)



#### KonnectControlPlaneAPIAuthConfiguration


KonnectControlPlaneAPIAuthConfiguration contains the configuration to authenticate with Konnect API ControlPlane.



| Field | Description |
| --- | --- |
| `clusterCertificateSecretRef` _[ClusterCertificateSecretRef](#clustercertificatesecretref)_ | ClusterCertificateSecretRef is the reference to the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane this KonnectExtension is associated with. |
| `controlPlaneRegion` _string_ | ControlPlaneRegion is the region of the Konnect Control Plane. |
| `serverHostname` _string_ | ServerHostname is the fully qualified domain name of the Konnect server. For typical operation a default value doesn't need to be adjusted. It matches the RFC 1123 definition of a hostname with 1 notable exception that numeric IP addresses are not allowed.<br /><br /> Note that as per RFC1035 and RFC1123, a *label* must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character. No other punctuation is allowed. |
| `konnectControlPlaneAPIAuthConfiguration` _[KonnectControlPlaneAPIAuthConfiguration](#konnectcontrolplaneapiauthconfiguration)_ | AuthConfiguration must be used to configure the Konnect API authentication. |
| `clusterDataPlaneLabels` _object (keys:string, values:string)_ | ClusterDataPlaneLabels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### LLMPrompt


LLMPrompt is a text prompt that includes parameters, a role and content.<br /><br />
This is intended for situations like when you need to provide roles in a
prompt to an LLM in order to influence its behavior and responses.<br /><br />
For example, you might want to provide a "system" role and tell the LLM
something like "you are a helpful assistant who responds in the style of
Sherlock Holmes".



| Field | Description |
| --- | --- |
| `content` _string_ | Content is the prompt text sent for inference. |
| `role` _[LLMPromptRole](#llmpromptrole)_ | Role indicates the role of the prompt. This is used to identify the prompt's purpose, such as "system" or "user" and can influence the behavior of the LLM.<br /><br /> If not specified, "user" will be used as the default. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LLMPromptParams


LLMPromptParams contains parameters that can be used to control the behavior
of a large language model (LLM) when generating text based on a prompt.



| Field | Description |
| --- | --- |
| `temperature` _string_ | Temperature controls the randomness of predictions by scaling the logits before applying softmax. A lower temperature (e.g., 0.0 to 0.7) makes the model more confident in its predictions, leading to more repetitive and deterministic outputs. A higher temperature (e.g., 0.8 to 1.0) increases randomness, generating more diverse and creative outputs. At very high temperatures, the outputs may become nonsensical or highly unpredictable. |
| `maxTokens` _integer_ | Max Tokens specifies the maximum length of the model's output in terms of the number of tokens (words or pieces of words). This parameter limits the output's size, ensuring the model generates content within a manageable scope. A token can be a word or part of a word, depending on the model's tokenizer. |
| `topK` _integer_ | TopK sampling is a technique where the model's prediction is limited to the K most likely next tokens at each step of the generation process. The probability distribution is truncated to these top K tokens, and the next token is randomly sampled from this subset. This method helps in reducing the chance of selecting highly improbable tokens, making the text more coherent. A smaller K leads to more predictable text, while a larger K allows for more diversity but with an increased risk of incoherence. |
| `topP` _string_ | TopP (also known as nucleus sampling) is an alternative to top K sampling. Instead of selecting the top K tokens, top P sampling chooses from the smallest set of tokens whose cumulative probability exceeds the threshold P. This method dynamically adjusts the number of tokens considered at each step, depending on their probability distribution. It helps in maintaining diversity while also avoiding very unlikely tokens. A higher P value increases diversity but can lead to less coherence, whereas a lower P value makes the model's outputs more focused and coherent. |


_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LLMPromptRole
_Underlying type:_ `string`

LLMPromptRole indicates the role of a prompt for a large language model (LLM).





_Appears in:_
- [LLMPrompt](#llmprompt)

#### LLMPromptType
_Underlying type:_ `string`

LLMPromptType indicates the type of prompt to be used for a large
language model (LLM).





_Appears in:_
- [CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel)

#### LargeLanguageModels


LargeLanguageModels is a list of Large Language Models (LLM) hosted in
various ways (cloud hosted, self hosted, e.t.c.) which the AIGateway should
serve and manage traffic for.



| Field | Description |
| --- | --- |
| `cloudHosted` _[CloudHostedLargeLanguageModel](#cloudhostedlargelanguagemodel) array_ | CloudHosted configures LLMs hosted and served by cloud providers.<br /><br /> This is currently a required field, requiring at least one cloud-hosted LLM be specified, however in future iterations we may add other hosting options such as self-hosted LLMs as separate fields. |


_Appears in:_
- [AIGatewaySpec](#aigatewayspec)

#### MetricsConfig


MetricsConfig holds the configuration for the DataPlane metrics.



| Field | Description |
| --- | --- |
| `latency` _boolean_ | Latency indicates whether latency metrics are enabled for the DataPlane. This translates into deployed instances having `latency_metrics` option set on the Prometheus plugin. |
| `bandwidth` _boolean_ | Bandwidth indicates whether bandwidth metrics are enabled for the DataPlane. This translates into deployed instances having `bandwidth_metrics` option set on the Prometheus plugin. |
| `upstreamHealth` _boolean_ | UpstreamHealth indicates whether upstream health metrics are enabled for the DataPlane. This translates into deployed instances having `upstream_health_metrics` option set on the Prometheus plugin. |
| `statusCode` _boolean_ | StatusCode indicates whether status code metrics are enabled for the DataPlane. This translates into deployed instances having `status_code_metrics` option set on the Prometheus plugin. |


_Appears in:_
- [DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)

#### ServiceSelector


ServiceSelector holds the service selector specification.



| Field | Description |
| --- | --- |
| `matchNames` _[ServiceSelectorEntry](#serviceselectorentry) array_ | MatchNames holds the list of Services names to match. |


_Appears in:_
- [DataPlaneMetricsExtensionSpec](#dataplanemetricsextensionspec)

#### ServiceSelectorEntry


ServiceSelectorEntry holds the name of a service to match.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the service to match. |


_Appears in:_
- [ServiceSelector](#serviceselector)

#### WatchNamespaceGrantFrom


WatchNamespaceGrantFrom describes trusted namespaces.



| Field | Description |
| --- | --- |
| `group` _string_ | Group is the group of the referent. |
| `kind` _string_ | Kind is the kind of the referent. |
| `namespace` _string_ | Namespace is the namespace of the referent. |


_Appears in:_
- [WatchNamespaceGrantSpec](#watchnamespacegrantspec)

#### WatchNamespaceGrantSpec


WatchNamespaceGrantSpec defines the desired state of an WatchNamespaceGrant.



| Field | Description |
| --- | --- |
| `from` _[WatchNamespaceGrantFrom](#watchnamespacegrantfrom) array_ | From describes the trusted namespaces and kinds that can reference the namespace this grant exists in. |


_Appears in:_
- [WatchNamespaceGrant](#watchnamespacegrant)


## gateway-operator.konghq.com/v1beta1

Package v1beta1 contains API Schema definitions for the gateway-operator.konghq.com v1beta1 API group.

- [ControlPlane](#controlplane)
- [DataPlane](#dataplane)
- [GatewayConfiguration](#gatewayconfiguration)
### ControlPlane


ControlPlane is the Schema for the controlplanes API

<!-- control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `ControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[ControlPlaneSpec](#controlplanespec)_ |  |



### DataPlane


DataPlane is the Schema for the dataplanes API

<!-- data_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `DataPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[DataPlaneSpec](#dataplanespec)_ |  |



### GatewayConfiguration


GatewayConfiguration is the Schema for the gatewayconfigurations API.

<!-- gateway_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v1beta1`
| `kind` _string_ | `GatewayConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[GatewayConfigurationSpec](#gatewayconfigurationspec)_ |  |



### Types

In this section you will find types that the CRDs rely on.
#### Address


Address describes an address which can be either an IP address or a hostname.



| Field | Description |
| --- | --- |
| `type` _[AddressType](#addresstype)_ | Type of the address. |
| `value` _string_ | Value of the address. The validity of the values will depend on the type and support by the controller.<br /><br /> Examples: `*******`, `128::1`, `my-ip-address`. |
| `sourceType` _[AddressSourceType](#addresssourcetype)_ | Source type of the address. |


_Appears in:_
- [DataPlaneStatus](#dataplanestatus)
- [RolloutStatusService](#rolloutstatusservice)

#### AddressSourceType
_Underlying type:_ `string`

AddressSourceType defines the type of source this address represents.<br /><br />
Can be one of:<br /><br />
* `PublicLoadBalancer`
* `PrivateLoadBalancer`
* `PublicIP`
* `PrivateIP`





_Appears in:_
- [Address](#address)

#### AddressType
_Underlying type:_ `string`

AddressType defines how a network address is represented as a text string.<br /><br />
Can be one of:<br /><br />
* `IPAddress`
* `Hostname`





_Appears in:_
- [Address](#address)

#### BlueGreenStrategy


BlueGreenStrategy defines the Blue Green deployment strategy.



| Field | Description |
| --- | --- |
| `promotion` _[Promotion](#promotion)_ | Promotion defines how the operator handles promotion of resources. |
| `resources` _[RolloutResources](#rolloutresources)_ | Resources controls what happens to operator managed resources during or after a rollout. |


_Appears in:_
- [RolloutStrategy](#rolloutstrategy)

#### ControlPlaneDeploymentOptions


ControlPlaneDeploymentOptions is a shared type used on objects to indicate that their
configuration results in a Deployment which is managed by the Operator and
includes options for managing Deployments such as the the number of replicas
or pod options like container image and resource requirements.
version, as well as Env variable overrides.



| Field | Description |
| --- | --- |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This only affects the DataPlane deployments for now, for more details on ControlPlane scaling please see https://github.com/Kong/gateway-operator/issues/736. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneOptions


ControlPlaneOptions indicates the specific information needed to
deploy and connect a ControlPlane to a DataPlane object.



| Field | Description |
| --- | --- |
| `deployment` _[ControlPlaneDeploymentOptions](#controlplanedeploymentoptions)_ |  |
| `dataplane` _string_ | DataPlanes refers to the named DataPlane objects which this ControlPlane is responsible for. Currently they must be in the same namespace as the DataPlane. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |


_Appears in:_
- [ControlPlaneSpec](#controlplanespec)
- [GatewayConfigurationSpec](#gatewayconfigurationspec)

#### ControlPlaneSpec


ControlPlaneSpec defines the desired state of ControlPlane



| Field | Description |
| --- | --- |
| `deployment` _[ControlPlaneDeploymentOptions](#controlplanedeploymentoptions)_ |  |
| `dataplane` _string_ | DataPlanes refers to the named DataPlane objects which this ControlPlane is responsible for. Currently they must be in the same namespace as the DataPlane. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `gatewayClass` _[ObjectName](#objectname)_ | GatewayClass indicates the Gateway resources which this ControlPlane should be responsible for configuring routes for (e.g. HTTPRoute, TCPRoute, UDPRoute, TLSRoute, e.t.c.).<br /><br /> Required for the ControlPlane to have any effect: at least one Gateway must be present for configuration to be pushed to the data-plane and only Gateway resources can be used to identify data-plane entities. |
| `ingressClass` _string_ | IngressClass enables support for the older Ingress resource and indicates which Ingress resources this ControlPlane should be responsible for.<br /><br /> Routing configured this way will be applied to the Gateway resources indicated by GatewayClass.<br /><br /> If omitted, Ingress resources will not be supported by the ControlPlane. |


_Appears in:_
- [ControlPlane](#controlplane)



#### DataPlaneDeploymentOptions


DataPlaneDeploymentOptions specifies options for the Deployments (as in the Kubernetes
resource "Deployment") which are created and managed for the DataPlane resource.



| Field | Description |
| --- | --- |
| `rollout` _[Rollout](#rollout)_ | Rollout describes a custom rollout strategy. |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This is effectively shorthand for setting a scaling minimum and maximum to the same value. This field and the scaling field are mutually exclusive: You can only configure one or the other. |
| `scaling` _[Scaling](#scaling)_ | Scaling defines the scaling options for the deployment. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. It's being applied on top of the generated Deployments using [StrategicMergePatch](https://pkg.go.dev/k8s.io/apimachinery/pkg/util/strategicpatch#StrategicMergePatch). |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### DataPlaneNetworkOptions


DataPlaneNetworkOptions defines network related options for a DataPlane.



| Field | Description |
| --- | --- |
| `services` _[DataPlaneServices](#dataplaneservices)_ | Services indicates the configuration of Kubernetes Services needed for the topology of various forms of traffic (including ingress, e.t.c.) to and from the DataPlane. |
| `konnectCertificate` _[KonnectCertificateOptions](#konnectcertificateoptions)_ | KonnectCA is the certificate authority that the operator uses to provision client certificates the DataPlane will use to authenticate itself to the Konnect API. Requires Enterprise. |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneOptions


DataPlaneOptions defines the information specifically needed to
deploy the DataPlane.



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[DataPlaneNetworkOptions](#dataplanenetworkoptions)_ |  |
| `resources` _[DataPlaneResources](#dataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the DataPlane. |


_Appears in:_
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneResources


DataPlaneResources defines the resources that will be created and managed
for the DataPlane.



| Field | Description |
| --- | --- |
| `podDisruptionBudget` _[PodDisruptionBudget](#poddisruptionbudget)_ | PodDisruptionBudget is the configuration for the PodDisruptionBudget that will be created for the DataPlane. |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)

#### DataPlaneRolloutStatus


DataPlaneRolloutStatus describes the DataPlane rollout status.



| Field | Description |
| --- | --- |
| `services` _[DataPlaneRolloutStatusServices](#dataplanerolloutstatusservices)_ | Services contain the information about the services which are available through which user can access the preview deployment. |
| `deployment` _[DataPlaneRolloutStatusDeployment](#dataplanerolloutstatusdeployment)_ | Deployment contains the information about the preview deployment. |
| `conditions` _[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#condition-v1-meta) array_ | Conditions contains the status conditions about the rollout. |


_Appears in:_
- [DataPlaneStatus](#dataplanestatus)

#### DataPlaneRolloutStatusDeployment


DataPlaneRolloutStatusDeployment is a rollout status field which contains
fields specific for Deployments during the rollout.



| Field | Description |
| --- | --- |
| `selector` _string_ | Selector is a stable label selector value assigned to a DataPlane rollout status which is used throughout the rollout as a deterministic labels selector for Services and Deployments. |


_Appears in:_
- [DataPlaneRolloutStatus](#dataplanerolloutstatus)

#### DataPlaneRolloutStatusServices


DataPlaneRolloutStatusServices describes the status of the services during
DataPlane rollout.



| Field | Description |
| --- | --- |
| `ingress` _[RolloutStatusService](#rolloutstatusservice)_ | Ingress contains the name and the address of the preview service for ingress. Using this service users can send requests that will hit the preview deployment. |
| `adminAPI` _[RolloutStatusService](#rolloutstatusservice)_ | AdminAPI contains the name and the address of the preview service for Admin API. Using this service users can send requests to configure the DataPlane's preview deployment. |


_Appears in:_
- [DataPlaneRolloutStatus](#dataplanerolloutstatus)

#### DataPlaneServiceOptions


DataPlaneServiceOptions contains Services related DataPlane configuration.



| Field | Description |
| --- | --- |
| `ports` _[DataPlaneServicePort](#dataplaneserviceport) array_ | Ports defines the list of ports that are exposed by the service. The ports field allows defining the name, port and targetPort of the underlying service ports, while the protocol is defaulted to TCP, as it is the only protocol currently supported. |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [DataPlaneServices](#dataplaneservices)

#### DataPlaneServicePort


DataPlaneServicePort contains information on service's port.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of this port within the service. This must be a DNS_LABEL. All ports within a ServiceSpec must have unique names. When considering the endpoints for a Service, this must match the 'name' field in the EndpointPort. Optional if only one ServicePort is defined on this service. |
| `port` _integer_ | The port that will be exposed by this service. |
| `targetPort` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | Number or name of the port to access on the pods targeted by the service. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME. If this is a string, it will be looked up as a named port in the target Pod's container ports. If this is not specified, the value of the 'port' field is used (an identity map). This field is ignored for services with clusterIP=None, and should be omitted or set equal to the 'port' field. More info: https://kubernetes.io/docs/concepts/services-networking/service/#defining-a-service |
| `nodePort` _integer_ | The port on each node on which this service is exposed when type is NodePort or LoadBalancer. Usually assigned by the system. If a value is specified, in-range, and not in use it will be used, otherwise the operation will fail. If not specified, a port will be allocated if this Service requires one. If this field is specified when creating a Service which does not need it, creation will fail. This field will be wiped when updating a Service to no longer need it (e.g. changing type from NodePort to ClusterIP).<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport<br /><br /> Can only be specified if type is NodePort or LoadBalancer. |


_Appears in:_
- [DataPlaneServiceOptions](#dataplaneserviceoptions)

#### DataPlaneServices


DataPlaneServices contains Services related DataPlane configuration, shared with the GatewayConfiguration.



| Field | Description |
| --- | --- |
| `ingress` _[DataPlaneServiceOptions](#dataplaneserviceoptions)_ | Ingress is the Kubernetes Service that will be used to expose ingress traffic for the DataPlane. Here you can determine whether the DataPlane will be exposed outside the cluster (e.g. using a LoadBalancer type Services) or only internally (e.g. ClusterIP), and inject any additional annotations you need on the service (for instance, if you need to influence a cloud provider LoadBalancer configuration). |


_Appears in:_
- [DataPlaneNetworkOptions](#dataplanenetworkoptions)

#### DataPlaneSpec


DataPlaneSpec defines the desired state of DataPlane



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[DataPlaneNetworkOptions](#dataplanenetworkoptions)_ |  |
| `resources` _[DataPlaneResources](#dataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the DataPlane. |


_Appears in:_
- [DataPlane](#dataplane)



#### DeploymentOptions


DeploymentOptions is a shared type used on objects to indicate that their
configuration results in a Deployment which is managed by the Operator and
includes options for managing Deployments such as the number of replicas
or pod options like container image and resource requirements.
version, as well as Env variable overrides.



| Field | Description |
| --- | --- |
| `replicas` _integer_ | Replicas describes the number of desired pods. This is a pointer to distinguish between explicit zero and not specified. This is effectively shorthand for setting a scaling minimum and maximum to the same value. This field and the scaling field are mutually exclusive: You can only configure one or the other. |
| `scaling` _[Scaling](#scaling)_ | Scaling defines the scaling options for the deployment. |
| `podTemplateSpec` _[PodTemplateSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#podtemplatespec-v1-core)_ | PodTemplateSpec defines PodTemplateSpec for Deployment's pods. It's being applied on top of the generated Deployments using [StrategicMergePatch](https://pkg.go.dev/k8s.io/apimachinery/pkg/util/strategicpatch#StrategicMergePatch). |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)

#### GatewayConfigDataPlaneNetworkOptions


GatewayConfigDataPlaneNetworkOptions defines network related options for a DataPlane.



| Field | Description |
| --- | --- |
| `services` _[GatewayConfigDataPlaneServices](#gatewayconfigdataplaneservices)_ | Services indicates the configuration of Kubernetes Services needed for the topology of various forms of traffic (including ingress, etc.) to and from the DataPlane. |


_Appears in:_
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### GatewayConfigDataPlaneOptions


GatewayConfigDataPlaneOptions indicates the specific information needed to
configure and deploy a DataPlane object.



| Field | Description |
| --- | --- |
| `deployment` _[DataPlaneDeploymentOptions](#dataplanedeploymentoptions)_ |  |
| `network` _[GatewayConfigDataPlaneNetworkOptions](#gatewayconfigdataplanenetworkoptions)_ |  |
| `resources` _[GatewayConfigDataPlaneResources](#gatewayconfigdataplaneresources)_ |  |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the DataPlane resources to influence or enhance functionality. NOTE: since we have one extension only (KonnectExtension), we limit the amount of extensions to 1. |
| `pluginsToInstall` _[NamespacedName](#namespacedname) array_ | PluginsToInstall is a list of KongPluginInstallation resources that will be installed and available in the Gateways (DataPlanes) that use this GatewayConfig. |


_Appears in:_
- [GatewayConfigurationSpec](#gatewayconfigurationspec)

#### GatewayConfigDataPlaneResources


GatewayConfigDataPlaneResources defines the resources that will be
created and managed for Gateway's DataPlane.



| Field | Description |
| --- | --- |
| `podDisruptionBudget` _[PodDisruptionBudget](#poddisruptionbudget)_ | PodDisruptionBudget is the configuration for the PodDisruptionBudget that will be created for the DataPlane. |


_Appears in:_
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)

#### GatewayConfigDataPlaneServices


GatewayConfigDataPlaneServices contains Services related DataPlane configuration.



| Field | Description |
| --- | --- |
| `ingress` _[GatewayConfigServiceOptions](#gatewayconfigserviceoptions)_ | Ingress is the Kubernetes Service that will be used to expose ingress traffic for the DataPlane. Here you can determine whether the DataPlane will be exposed outside the cluster (e.g. using a LoadBalancer type Services) or only internally (e.g. ClusterIP), and inject any additional annotations you need on the service (for instance, if you need to influence a cloud provider LoadBalancer configuration). |


_Appears in:_
- [GatewayConfigDataPlaneNetworkOptions](#gatewayconfigdataplanenetworkoptions)

#### GatewayConfigServiceOptions


GatewayConfigServiceOptions is used to includes options to customize the ingress service,
such as the annotations.



| Field | Description |
| --- | --- |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [GatewayConfigDataPlaneServices](#gatewayconfigdataplaneservices)

#### GatewayConfigurationSpec


GatewayConfigurationSpec defines the desired state of GatewayConfiguration



| Field | Description |
| --- | --- |
| `dataPlaneOptions` _[GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)_ | DataPlaneOptions is the specification for configuration overrides for DataPlane resources that will be created for the Gateway. |
| `controlPlaneOptions` _[ControlPlaneOptions](#controlplaneoptions)_ | ControlPlaneOptions is the specification for configuration overrides for ControlPlane resources that will be created for the Gateway. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the Gateway resource to influence or enhance functionality. NOTE: currently, there's only 1 extension that can be attached at the Gateway level (KonnectExtension), so the amount of extensions is limited to 1. |


_Appears in:_
- [GatewayConfiguration](#gatewayconfiguration)





#### HorizontalScaling


HorizontalScaling defines horizontal scaling options for the deployment.
It holds all the options from the HorizontalPodAutoscalerSpec besides the
ScaleTargetRef which is being controlled by the Operator.



| Field | Description |
| --- | --- |
| `minReplicas` _integer_ | minReplicas is the lower limit for the number of replicas to which the autoscaler can scale down.  It defaults to 1 pod.  minReplicas is allowed to be 0 if the alpha feature gate HPAScaleToZero is enabled and at least one Object or External metric is configured.  Scaling is active as long as at least one metric value is available. |
| `maxReplicas` _integer_ | maxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up. It cannot be less that minReplicas. |
| `metrics` _[MetricSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#metricspec-v2-autoscaling) array_ | metrics contains the specifications for which to use to calculate the desired replica count (the maximum replica count across all metrics will be used).  The desired replica count is calculated multiplying the ratio between the target value and the current value by the current number of pods.  Ergo, metrics used must decrease as the pod count is increased, and vice-versa.  See the individual metric source types for more information about how each type of metric must respond. If not set, the default metric will be set to 80% average CPU utilization. |
| `behavior` _[HorizontalPodAutoscalerBehavior](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#horizontalpodautoscalerbehavior-v2-autoscaling)_ | behavior configures the scaling behavior of the target in both Up and Down directions (scaleUp and scaleDown fields respectively). If not set, the default HPAScalingRules for scale up and scale down are used. |


_Appears in:_
- [Scaling](#scaling)

#### KonnectCertificateOptions


KonnectCertificateOptions indicates how the operator should manage the certificates that managed entities will use
to interact with Konnect.



| Field | Description |
| --- | --- |
| `issuer` _[NamespacedName](#namespacedname)_ | Issuer is the cert-manager Issuer or ClusterIssuer the operator will use to request certificates. When Namespace is set, the operator will retrieve the Issuer with that Name in that Namespace. When Namespace is omitted, the operator will retrieve the ClusterIssuer with that name. |


_Appears in:_
- [DataPlaneNetworkOptions](#dataplanenetworkoptions)

#### NamespacedName


NamespacedName is a resource identified by name and optional namespace.



| Field | Description |
| --- | --- |
| `namespace` _string_ |  |
| `name` _string_ |  |


_Appears in:_
- [DataPlaneOptions](#dataplaneoptions)
- [DataPlaneSpec](#dataplanespec)
- [GatewayConfigDataPlaneOptions](#gatewayconfigdataplaneoptions)
- [KonnectCertificateOptions](#konnectcertificateoptions)

#### PodDisruptionBudget


PodDisruptionBudget defines the configuration for the PodDisruptionBudget.



| Field | Description |
| --- | --- |
| `spec` _[PodDisruptionBudgetSpec](#poddisruptionbudgetspec)_ | Spec defines the specification of the PodDisruptionBudget. Selector is managed by the controller and cannot be set by the user. |


_Appears in:_
- [DataPlaneResources](#dataplaneresources)
- [GatewayConfigDataPlaneResources](#gatewayconfigdataplaneresources)

#### PodDisruptionBudgetSpec


PodDisruptionBudgetSpec defines the specification of a PodDisruptionBudget.



| Field | Description |
| --- | --- |
| `minAvailable` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | An eviction is allowed if at least "minAvailable" pods selected by "selector" will still be available after the eviction, i.e. even in the absence of the evicted pod.  So for example you can prevent all voluntary evictions by specifying "100%". |
| `maxUnavailable` _[IntOrString](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#intorstring-intstr-util)_ | An eviction is allowed if at most "maxUnavailable" pods selected by "selector" are unavailable after the eviction, i.e. even in absence of the evicted pod. For example, one can prevent all voluntary evictions by specifying 0. This is a mutually exclusive setting with "minAvailable". |
| `unhealthyPodEvictionPolicy` _[UnhealthyPodEvictionPolicyType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#unhealthypodevictionpolicytype-v1-policy)_ | UnhealthyPodEvictionPolicy defines the criteria for when unhealthy pods should be considered for eviction. Current implementation considers healthy pods, as pods that have status.conditions item with type="Ready",status="True".<br /><br /> Valid policies are IfHealthyBudget and AlwaysAllow. If no policy is specified, the default behavior will be used, which corresponds to the IfHealthyBudget policy.<br /><br /> IfHealthyBudget policy means that running pods (status.phase="Running"), but not yet healthy can be evicted only if the guarded application is not disrupted (status.currentHealthy is at least equal to status.desiredHealthy). Healthy pods will be subject to the PDB for eviction.<br /><br /> AlwaysAllow policy means that all running pods (status.phase="Running"), but not yet healthy are considered disrupted and can be evicted regardless of whether the criteria in a PDB is met. This means perspective running pods of a disrupted application might not get a chance to become healthy. Healthy pods will be subject to the PDB for eviction.<br /><br /> Additional policies may be added in the future. Clients making eviction decisions should disallow eviction of unhealthy pods if they encounter an unrecognized policy in this field.<br /><br /> This field is beta-level. The eviction API uses this field when the feature gate PDBUnhealthyPodEvictionPolicy is enabled (enabled by default). |


_Appears in:_
- [PodDisruptionBudget](#poddisruptionbudget)

#### Promotion


Promotion is a type that contains fields that define how the operator handles
promotion of resources during a blue/green rollout.



| Field | Description |
| --- | --- |
| `strategy` _[PromotionStrategy](#promotionstrategy)_ | Strategy indicates how you want the operator to handle the promotion of the preview (green) resources (Deployments and Services) after all workflows and tests succeed, OR if you even want it to break before performing the promotion to allow manual inspection. |


_Appears in:_
- [BlueGreenStrategy](#bluegreenstrategy)

#### PromotionStrategy
_Underlying type:_ `string`

PromotionStrategy is the type of promotion strategy consts.<br /><br />
Allowed values:<br /><br />
  - `BreakBeforePromotion` is a promotion strategy which will ensure all new
    resources are ready and then break, to enable manual inspection.
    The user must indicate manually when they want the promotion to continue.
    That can be done by annotating the `DataPlane` object with
    `"gateway-operator.konghq.com/promote-when-ready": "true"`.





_Appears in:_
- [Promotion](#promotion)

#### Rollout


Rollout defines options for rollouts.



| Field | Description |
| --- | --- |
| `strategy` _[RolloutStrategy](#rolloutstrategy)_ | Strategy contains the deployment strategy for rollout. |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)

#### RolloutResourcePlan
_Underlying type:_ `[struct{Deployment RolloutResourcePlanDeployment "json:\"deployment,omitempty\""}](#struct{deployment-rolloutresourceplandeployment-"json:\"deployment,omitempty\""})`

RolloutResourcePlan is a type that holds rollout resource plan related fields
which control how the operator handles resources during and after a rollout.





_Appears in:_
- [RolloutResources](#rolloutresources)



#### RolloutResources


RolloutResources is the type which contains the fields which control how the operator
manages the resources it manages during or after the rollout concludes.



| Field | Description |
| --- | --- |
| `plan` _[RolloutResourcePlan](#rolloutresourceplan)_ | Plan defines the resource plan for managing resources during and after a rollout. |


_Appears in:_
- [BlueGreenStrategy](#bluegreenstrategy)

#### RolloutStatusService


RolloutStatusService is a struct which contains status information about
services that are exposed as part of the rollout.



| Field | Description |
| --- | --- |
| `name` _string_ | Name indicates the name of the service. |
| `addresses` _[Address](#address) array_ | Addresses contains the addresses of a Service. |


_Appears in:_
- [DataPlaneRolloutStatusServices](#dataplanerolloutstatusservices)

#### RolloutStrategy


RolloutStrategy holds the rollout strategy options.



| Field | Description |
| --- | --- |
| `blueGreen` _[BlueGreenStrategy](#bluegreenstrategy)_ | BlueGreen holds the options specific for Blue Green Deployments. |


_Appears in:_
- [Rollout](#rollout)

#### Scaling


Scaling defines the scaling options for the deployment.



| Field | Description |
| --- | --- |
| `horizontal` _[HorizontalScaling](#horizontalscaling)_ | HorizontalScaling defines horizontal scaling options for the deployment. |


_Appears in:_
- [DataPlaneDeploymentOptions](#dataplanedeploymentoptions)
- [DeploymentOptions](#deploymentoptions)

#### ServiceOptions


ServiceOptions is used to includes options to customize the ingress service,
such as the annotations.



| Field | Description |
| --- | --- |
| `type` _[ServiceType](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#servicetype-v1-core)_ | Type determines how the Service is exposed. Defaults to `LoadBalancer`.<br /><br /> `ClusterIP` allocates a cluster-internal IP address for load-balancing to endpoints.<br /><br /> `NodePort` exposes the Service on each Node's IP at a static port (the NodePort). To make the node port available, Kubernetes sets up a cluster IP address, the same as if you had requested a Service of type: ClusterIP.<br /><br /> `LoadBalancer` builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP.<br /><br /> More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types |
| `name` _string_ | Name defines the name of the service. If Name is empty, the controller will generate a service name from the owning object. |
| `annotations` _object (keys:string, values:string)_ | Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.<br /><br /> More info: http://kubernetes.io/docs/user-guide/annotations |
| `externalTrafficPolicy` _[ServiceExternalTrafficPolicy](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#serviceexternaltrafficpolicy-v1-core)_ | ExternalTrafficPolicy describes how nodes distribute service traffic they receive on one of the Service's "externally-facing" addresses (NodePorts, ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure the service in a way that assumes that external load balancers will take care of balancing the service traffic between nodes, and so each node will deliver traffic only to the node-local endpoints of the service, without masquerading the client source IP. (Traffic mistakenly sent to a node with no endpoints will be dropped.) The default value, "Cluster", uses the standard behavior of routing to all endpoints evenly (possibly modified by topology and other features). Note that traffic sent to an External IP or LoadBalancer IP from within the cluster will always get "Cluster" semantics, but clients sending to a NodePort from within the cluster may need to take traffic policy into account when picking a node.<br /><br /> More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip |


_Appears in:_
- [DataPlaneServiceOptions](#dataplaneserviceoptions)
- [GatewayConfigServiceOptions](#gatewayconfigserviceoptions)

#### WatchNamespaces


WatchNamespaces defines the namespaces to watch for resources



| Field | Description |
| --- | --- |
| `type` _[WatchNamespacesType](#watchnamespacestype)_ | Type indicates the type of namespace watching to be done. By default, all namespaces are watched. |
| `list` _string array_ | List is a list of namespaces to watch for resources. Only used when Type is set to List. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneSpec](#controlplanespec)

#### WatchNamespacesType
_Underlying type:_ `string`

WatchNamespacesType indicates the type of namespace watching to be done.





_Appears in:_
- [WatchNamespaces](#watchnamespaces)


## gateway-operator.konghq.com/v2alpha1

Package v2alpha1 contains API Schema definitions for the gateway-operator.konghq.com v2alpha1 API group.

Package v2alpha1 contains API Schema definitions for the gateway-operator.konghq.com v2alpha1 API group

- [ControlPlane](#controlplane)
### ControlPlane


ControlPlane is the Schema for the controlplanes API

<!-- control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `gateway-operator.konghq.com/v2alpha1`
| `kind` _string_ | `ControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[ControlPlaneSpec](#controlplanespec)_ | Spec is the specification of the ControlPlane resource. |



### Types

In this section you will find types that the CRDs rely on.
#### ControlPlaneAdminAPI


ControlPlaneAdminAPI defines the configuration for the DataPlane Kong Admin API.



| Field | Description |
| --- | --- |
| `workspace` _string_ | Workspace indicates the Kong Workspace to use for the ControlPlane. If left empty then no Kong workspace will be used. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneController


ControlPlaneController defines a controller state for the ControlPlane.
It overrides the default behavior as defined in the deployed operator version.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the controller. |
| `state` _[ControllerState](#controllerstate)_ | State indicates whether the feature gate is enabled or disabled. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneStatus](#controlplanestatus)

#### ControlPlaneDataPlaneTarget


ControlPlaneDataPlaneTarget defines the target for the DataPlane that the ControlPlane
is responsible for configuring.



| Field | Description |
| --- | --- |
| `type` _[ControlPlaneDataPlaneTargetType](#controlplanedataplanetargettype)_ | Type indicates the type of the DataPlane target. |
| `external` _[ControlPlaneDataPlaneTargetExternal](#controlplanedataplanetargetexternal)_ | External is the External of the DataPlane target. This is used for configuring externally managed DataPlanes like those installed independently with Helm. |
| `ref` _[ControlPlaneDataPlaneTargetRef](#controlplanedataplanetargetref)_ | Ref is the name of the DataPlane to configure. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneDataPlaneTargetExternal


ControlPlaneDataPlaneTargetExternal defines the configuration for an external DataPlane
that the ControlPlane is responsible for configuring.



| Field | Description |
| --- | --- |
| `url` _string_ | URL is the URL of the external DataPlane to configure. |


_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneDataPlaneTargetRef


ControlPlaneDataPlaneTargetRef defines the reference to a DataPlane resource
that the ControlPlane is responsible for configuring.



| Field | Description |
| --- | --- |
| `name` _string_ | Ref is the name of the DataPlane to configure. |


_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneDataPlaneTargetType
_Underlying type:_ `string`

ControlPlaneDataPlaneTargetType defines the type of the DataPlane target
that the ControlPlane is responsible for configuring.





_Appears in:_
- [ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)

#### ControlPlaneFeatureGate


ControlPlaneFeatureGate defines a feature gate state for the ControlPlane.
It overrides the default behavior as defined in the deployed operator version.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the feature gate. |
| `state` _[FeatureGateState](#featuregatestate)_ | State indicates whether the feature gate is enabled or disabled. |


_Appears in:_
- [ControlPlaneOptions](#controlplaneoptions)
- [ControlPlaneSpec](#controlplanespec)
- [ControlPlaneStatus](#controlplanestatus)

#### ControlPlaneOptions


ControlPlaneOptions indicates the specific information needed to
deploy and connect a ControlPlane to a DataPlane object.



| Field | Description |
| --- | --- |
| `dataplane` _[ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)_ | DataPlane designates the target data plane to configure.<br /><br /> It can be either a URL to an externally managed DataPlane (e.g. installed independently with Helm) or a name of a DataPlane resource that is managed by the operator. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `featureGates` _[ControlPlaneFeatureGate](#controlplanefeaturegate) array_ | FeatureGates is a list of feature gates that are enabled for this ControlPlane. |
| `controllers` _[ControlPlaneController](#controlplanecontroller) array_ | Controllers defines the controllers that are enabled for this ControlPlane. |
| `adminAPI` _[ControlPlaneAdminAPI](#controlplaneadminapi)_ | AdminAPI defines the configuration for the Kong Admin API. |


_Appears in:_
- [ControlPlaneSpec](#controlplanespec)

#### ControlPlaneSpec


ControlPlaneSpec defines the desired state of ControlPlane



| Field | Description |
| --- | --- |
| `dataplane` _[ControlPlaneDataPlaneTarget](#controlplanedataplanetarget)_ | DataPlane designates the target data plane to configure.<br /><br /> It can be either a URL to an externally managed DataPlane (e.g. installed independently with Helm) or a name of a DataPlane resource that is managed by the operator. |
| `extensions` _ExtensionRef array_ | Extensions provide additional or replacement features for the ControlPlane resources to influence or enhance functionality. |
| `watchNamespaces` _[WatchNamespaces](#watchnamespaces)_ | WatchNamespaces indicates the namespaces to watch for resources. |
| `featureGates` _[ControlPlaneFeatureGate](#controlplanefeaturegate) array_ | FeatureGates is a list of feature gates that are enabled for this ControlPlane. |
| `controllers` _[ControlPlaneController](#controlplanecontroller) array_ | Controllers defines the controllers that are enabled for this ControlPlane. |
| `adminAPI` _[ControlPlaneAdminAPI](#controlplaneadminapi)_ | AdminAPI defines the configuration for the Kong Admin API. |
| `ingressClass` _string_ | IngressClass enables support for the older Ingress resource and indicates which Ingress resources this ControlPlane should be responsible for.<br /><br /> If omitted, Ingress resources will not be supported by the ControlPlane. |


_Appears in:_
- [ControlPlane](#controlplane)



#### ControllerState
_Underlying type:_ `string`

ControllerState defines the state of a feature gate.





_Appears in:_
- [ControlPlaneController](#controlplanecontroller)

#### FeatureGateState
_Underlying type:_ `string`

FeatureGateState defines the state of a feature gate.





_Appears in:_
- [ControlPlaneFeatureGate](#controlplanefeaturegate)

