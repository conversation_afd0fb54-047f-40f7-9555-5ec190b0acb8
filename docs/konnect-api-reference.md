<!-- This document is generated by kong/kubernetes-configuration's 'generate.docs' make target, DO NOT EDIT -->

## Packages
- [konnect.konghq.com/v1alpha1](#konnectkonghqcomv1alpha1)
- [konnect.konghq.com/v1alpha2](#konnectkonghqcomv1alpha2)


## konnect.konghq.com/v1alpha1

Package v1alpha1 contains API Schema definitions for the konnect.konghq.com v1alpha1 API group.

- [KonnectAPIAuthConfiguration](#konnectapiauthconfiguration)
- [KonnectCloudGatewayDataPlaneGroupConfiguration](#konnectcloudgatewaydataplanegroupconfiguration)
- [KonnectCloudGatewayNetwork](#konnectcloudgatewaynetwork)
- [KonnectCloudGatewayTransitGateway](#konnectcloudgatewaytransitgateway)
- [KonnectExtension](#konnectextension)
- [KonnectGatewayControlPlane](#konnectgatewaycontrolplane)
### KonnectAPIAuthConfiguration


KonnectAPIAuthConfiguration is the Schema for the Konnect configuration type.

<!-- konnect_api_auth_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectAPIAuthConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectAPIAuthConfigurationSpec](#konnectapiauthconfigurationspec)_ | Spec is the specification of the KonnectAPIAuthConfiguration resource. |



### KonnectCloudGatewayDataPlaneGroupConfiguration


KonnectCloudGatewayDataPlaneGroupConfiguration is the Schema for the Konnect Network API.

<!-- konnect_cloud_gateway_data_plane_group_configuration description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayDataPlaneGroupConfiguration`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayDataPlaneGroupConfigurationSpec](#konnectcloudgatewaydataplanegroupconfigurationspec)_ | Spec defines the desired state of KonnectCloudGatewayDataPlaneGroupConfiguration. |



### KonnectCloudGatewayNetwork


KonnectCloudGatewayNetwork is the Schema for the Konnect Network API.

<!-- konnect_cloud_gateway_network description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayNetwork`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayNetworkSpec](#konnectcloudgatewaynetworkspec)_ | Spec defines the desired state of KonnectCloudGatewayNetwork. |



### KonnectCloudGatewayTransitGateway


KonnectCloudGatewayTransitGateway is the Schema for the Konnect Transit Gateway API.

<!-- konnect_cloud_gateway_transit_gateway description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectCloudGatewayTransitGateway`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)_ | Spec defines the desired state of KonnectCloudGatewayTransitGateway. |



### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
deployment(s) spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### KonnectGatewayControlPlane


KonnectGatewayControlPlane is the Schema for the KonnectGatewayControlplanes API.

<!-- konnect_gateway_control_plane description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha1`
| `kind` _string_ | `KonnectGatewayControlPlane`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)_ | Spec defines the desired state of KonnectGatewayControlPlane. |



### Types

In this section you will find types that the CRDs rely on.


#### AWSTransitGateway


AWSTransitGateway is the configuration of an AWS transit gateway.



| Field | Description |
| --- | --- |
| `name` _string_ | Human-readable name of the transit gateway. |
| `dns_config` _[TransitGatewayDNSConfig](#transitgatewaydnsconfig) array_ | List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway attachment. |
| `cidr_blocks` _string array_ | CIDR blocks for constructing a route table for the transit gateway, when attaching to the owning network. |
| `attachment_config` _[AwsTransitGatewayAttachmentConfig](#awstransitgatewayattachmentconfig)_ | configuration to attach to AWS transit gateway on the AWS side. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)

#### AwsTransitGatewayAttachmentConfig


AwsTransitGatewayAttachmentConfig is the configuration to attach to a AWS transit gateway.



| Field | Description |
| --- | --- |
| `transit_gateway_id` _string_ | TransitGatewayID is the AWS transit gateway ID to create attachment to. |
| `ram_share_arn` _string_ | RAMShareArn is the resource share ARN to verify request to create transit gateway attachment. |


_Appears in:_
- [AWSTransitGateway](#awstransitgateway)

#### AzureTransitGateway


AzureTransitGateway is the configuration of an Azure transit gateway.



| Field | Description |
| --- | --- |
| `name` _string_ | Human-readable name of the transit gateway. |
| `dns_config` _[TransitGatewayDNSConfig](#transitgatewaydnsconfig) array_ | List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway attachment. |
| `attachment_config` _[AzureVNETPeeringAttachmentConfig](#azurevnetpeeringattachmentconfig)_ | configuration to attach to Azure VNET peering gateway. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)

#### AzureVNETPeeringAttachmentConfig


AzureVNETPeeringAttachmentConfig is the configuration to attach to a Azure VNET peering gateway.



| Field | Description |
| --- | --- |
| `tenant_id` _string_ | TenantID is the tenant ID for the Azure VNET Peering attachment. |
| `subscription_id` _string_ | SubscriptionID is the subscription ID for the Azure VNET Peering attachment. |
| `resource_group_name` _string_ | ResourceGroupName is the resource group name for the Azure VNET Peering attachment. |
| `vnet_name` _string_ | VnetName is the VNET Name for the Azure VNET Peering attachment. |


_Appears in:_
- [AzureTransitGateway](#azuretransitgateway)

#### CertificateSecret


CertificateSecret contains the information to access the client certificate.



| Field | Description |
| --- | --- |
| `provisioning` _[ProvisioningMethod](#provisioningmethod)_ | Provisioning is the method used to provision the certificate. It can be either Manual or Automatic. In case manual provisioning is used, the certificate must be provided by the user. In case automatic provisioning is used, the certificate will be automatically generated by the system. |
| `secretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionClientAuth](#konnectextensionclientauth)

#### ConfigurationDataPlaneGroupAutoscale


ConfigurationDataPlaneGroupAutoscale specifies the autoscale configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `static` _[ConfigurationDataPlaneGroupAutoscaleStatic](#configurationdataplanegroupautoscalestatic)_ | Static specifies the static configuration for the data-plane group. |
| `autopilot` _[ConfigurationDataPlaneGroupAutoscaleAutopilot](#configurationdataplanegroupautoscaleautopilot)_ | Autopilot specifies the autoscale configuration for the data-plane group. |
| `type` _[ConfigurationDataPlaneGroupAutoscaleType](#configurationdataplanegroupautoscaletype)_ | Type of autoscaling to use. |


_Appears in:_
- [KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup)

#### ConfigurationDataPlaneGroupAutoscaleAutopilot


ConfigurationDataPlaneGroupAutoscaleAutopilot specifies the autoscale configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `base_rps` _integer_ | Base number of requests per second that the deployment target should support. |
| `max_rps` _integer_ | Max number of requests per second that the deployment target should support. If not set, this defaults to 10x base_rps. |


_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupAutoscaleStatic


ConfigurationDataPlaneGroupAutoscaleStatic specifies the static configuration for the data-plane group.



| Field | Description |
| --- | --- |
| `instance_type` _[InstanceTypeName](#instancetypename)_ | Instance type name to indicate capacity. Currently supported values are small, medium, large but this list might be expanded in the future. For all the allowed values, please refer to the Konnect API documentation at https://docs.konghq.com/konnect/api/cloud-gateways/latest/#/Data-Plane%20Group%20Configurations/create-configuration. |
| `requested_instances` _integer_ | Number of data-planes the deployment target will contain. |


_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupAutoscaleType
_Underlying type:_ `string`

ConfigurationDataPlaneGroupAutoscaleType is the type of autoscale configuration for the data-plane group.





_Appears in:_
- [ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)

#### ConfigurationDataPlaneGroupEnvironmentField


ConfigurationDataPlaneGroupEnvironmentField specifies an environment variable field for the data-plane group.



| Field | Description |
| --- | --- |
| `name` _string_ | Name of the environment variable field to set for the data-plane group. Must be prefixed by KONG_. |
| `value` _string_ | Value assigned to the environment variable field for the data-plane group. |


_Appears in:_
- [KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup)

#### CreateControlPlaneRequest


CreateControlPlaneRequest - The request schema for the create control plane request.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of the control plane. |
| `description` _string_ | The description of the control plane in Konnect. |
| `cluster_type` _[CreateControlPlaneRequestClusterType](#createcontrolplanerequestclustertype)_ | The ClusterType value of the cluster associated with the Control Plane. |
| `auth_type` _[AuthType](#authtype)_ | The auth type value of the cluster associated with the Runtime Group. |
| `cloud_gateway` _boolean_ | Whether this control-plane can be used for cloud-gateways. |
| `proxy_urls` _ProxyURL array_ | Array of proxy URLs associated with reaching the data-planes connected to a control-plane. |
| `labels` _object (keys:string, values:string)_ | Labels store metadata of an entity that can be used for filtering an entity list or for searching across entity types.<br /><br /> Keys must be of length 1-63 characters, and cannot start with "kong", "konnect", "mesh", "kic", or "_". |


_Appears in:_
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### DataPlaneClientAuthStatus


DataPlaneClientAuthStatus contains the status information related to the ClientAuth configuration.



| Field | Description |
| --- | --- |
| `certificateSecretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### DataPlaneLabelValue
_Underlying type:_ `string`

DataPlaneLabelValue is the type that defines the value of a label that will be applied to the Konnect DataPlane.





_Appears in:_
- [KonnectExtensionDataPlane](#konnectextensiondataplane)

#### KonnectAPIAuthConfigurationRef


KonnectAPIAuthConfigurationRef is a reference to a KonnectAPIAuthConfiguration resource.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the KonnectAPIAuthConfiguration resource. |


_Appears in:_
- [KonnectConfiguration](#konnectconfiguration)

#### KonnectAPIAuthConfigurationSpec


KonnectAPIAuthConfigurationSpec is the specification of the KonnectAPIAuthConfiguration resource.



| Field | Description |
| --- | --- |
| `type` _[KonnectAPIAuthType](#konnectapiauthtype)_ |  |
| `token` _string_ | Token is the Konnect token used to authenticate with the Konnect API. |
| `secretRef` _[SecretReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#secretreference-v1-core)_ | SecretRef is a reference to a Kubernetes Secret containing the Konnect token. This secret is required to have the konghq.com/credential label set to "konnect". |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server. It can be either a full URL with an HTTPs scheme or just a hostname. Please refer to https://docs.konghq.com/konnect/network/ for the list of supported hostnames. |


_Appears in:_
- [KonnectAPIAuthConfiguration](#konnectapiauthconfiguration)



#### KonnectAPIAuthType
_Underlying type:_ `string`

KonnectAPIAuthType is the type of authentication used to authenticate with the Konnect API.





_Appears in:_
- [KonnectAPIAuthConfigurationSpec](#konnectapiauthconfigurationspec)

#### KonnectCloudGatewayDataPlaneGroupConfigurationSpec


KonnectCloudGatewayDataPlaneGroupConfigurationSpec defines the desired state of KonnectCloudGatewayDataPlaneGroupConfiguration.



| Field | Description |
| --- | --- |
| `version` _string_ | Version specifies the desired Kong Gateway version. |
| `dataplane_groups` _[KonnectConfigurationDataPlaneGroup](#konnectconfigurationdataplanegroup) array_ | DataplaneGroups is a list of desired data-plane groups that describe where to deploy instances, along with how many instances. |
| `api_access` _[APIAccess](#apiaccess)_ | APIAccess is the desired type of API access for data-plane groups. |
| `controlPlaneRef` _[ControlPlaneRef](#controlplaneref)_ | ControlPlaneRef is a reference to a ControlPlane which DataPlanes from this configuration will connect to. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfiguration](#konnectcloudgatewaydataplanegroupconfiguration)



#### KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup


KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup defines the observed state of a deployed data-plane group.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the ID of the deployed data-plane group. |
| `cloud_gateway_network_id` _string_ | CloudGatewayNetworkID is the ID of the cloud gateway network. |
| `provider` _[ProviderName](#providername)_ | Name of cloud provider. |
| `region` _string_ | Region ID for cloud provider region. |
| `private_ip_addresses` _string array_ | PrivateIPAddresses is a list of private IP addresses of the internal load balancer that proxies traffic to this data-plane group. |
| `egress_ip_addresses` _string array_ | EgressIPAddresses is a list of egress IP addresses for the network that this data-plane group runs on. |
| `state` _string_ | State is the current state of the data plane group. Can be e.g. initializing, ready, terminating. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)

#### KonnectCloudGatewayNetworkSpec


KonnectCloudGatewayNetworkSpec defines the desired state of KonnectCloudGatewayNetwork.



| Field | Description |
| --- | --- |
| `name` _string_ | Specifies the name of the network on Konnect. |
| `cloud_gateway_provider_account_id` _string_ | Specifies the provider Account ID. |
| `region` _string_ | Region ID for cloud provider region. |
| `availability_zones` _string array_ | List of availability zones that the network is attached to. |
| `cidr_block` _string_ | CIDR block configuration for the network. |
| `state` _[NetworkCreateState](#networkcreatestate)_ | Initial state for creating a network. |
| `konnect` _[KonnectConfiguration](#konnectconfiguration)_ |  |


_Appears in:_
- [KonnectCloudGatewayNetwork](#konnectcloudgatewaynetwork)



#### KonnectCloudGatewayTransitGatewaySpec


KonnectCloudGatewayTransitGatewaySpec defines the desired state of KonnectCloudGatewayTransitGateway.



| Field | Description |
| --- | --- |
| `networkRef` _[ObjectRef](#objectref)_ | NetworkRef is the schema for the NetworkRef type. |
| `type` _[TransitGatewayType](#transitgatewaytype)_ | Type is the type of the Konnect transit gateway. |
| `awsTransitGateway` _[AWSTransitGateway](#awstransitgateway)_ | AWSTransitGateway is the configuration of an AWS transit gateway. Used when type is "AWS Transit Gateway". |
| `azureTransitGateway` _[AzureTransitGateway](#azuretransitgateway)_ | AzureTransitGateway is the configuration of an Azure transit gateway. Used when type is "Azure Transit Gateway". |


_Appears in:_
- [KonnectCloudGatewayTransitGateway](#konnectcloudgatewaytransitgateway)



#### KonnectConfiguration


KonnectConfiguration is the Schema for the KonnectConfiguration API.



| Field | Description |
| --- | --- |
| `authRef` _[KonnectAPIAuthConfigurationRef](#konnectapiauthconfigurationref)_ | APIAuthConfigurationRef is the reference to the API Auth Configuration that should be used for this Konnect Configuration. |


_Appears in:_
- [KonnectCloudGatewayNetworkSpec](#konnectcloudgatewaynetworkspec)
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### KonnectConfigurationDataPlaneGroup


KonnectConfigurationDataPlaneGroup is the schema for the KonnectConfiguration type.



| Field | Description |
| --- | --- |
| `provider` _[ProviderName](#providername)_ | Name of cloud provider. |
| `region` _string_ | Region for cloud provider region. |
| `networkRef` _[ObjectRef](#objectref)_ | NetworkRef is the reference to the network that this data-plane group will be deployed on.<br /><br /> Cross namespace references are not supported for networkRef of type namespacedRef. This will be enforced in the future but currently (due to limitation in CEL validation in Kubernetes 1.31 and older) it is not. |
| `autoscale` _[ConfigurationDataPlaneGroupAutoscale](#configurationdataplanegroupautoscale)_ | Autoscale configuration for the data-plane group. |
| `environment` _[ConfigurationDataPlaneGroupEnvironmentField](#configurationdataplanegroupenvironmentfield) array_ | Array of environment variables to set for a data-plane group. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationSpec](#konnectcloudgatewaydataplanegroupconfigurationspec)

#### KonnectEndpoints


KonnectEndpoints defines the Konnect endpoints for the control plane.



| Field | Description |
| --- | --- |
| `telemetry` _string_ | TelemetryEndpoint is the endpoint for telemetry. |
| `controlPlane` _string_ | ControlPlaneEndpoint is the endpoint for the control plane. |


_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)
- [KonnectGatewayControlPlaneStatus](#konnectgatewaycontrolplanestatus)

#### KonnectEntityStatus


KonnectEntityStatus represents the status of a Konnect entity.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)
- [KonnectCloudGatewayNetworkStatus](#konnectcloudgatewaynetworkstatus)
- [KonnectCloudGatewayTransitGatewayStatus](#konnectcloudgatewaytransitgatewaystatus)
- [KonnectEntityStatusWithControlPlaneAndCertificateRefs](#konnectentitystatuswithcontrolplaneandcertificaterefs)
- [KonnectEntityStatusWithControlPlaneAndConsumerRefs](#konnectentitystatuswithcontrolplaneandconsumerrefs)
- [KonnectEntityStatusWithControlPlaneAndKeySetRef](#konnectentitystatuswithcontrolplaneandkeysetref)
- [KonnectEntityStatusWithControlPlaneAndServiceRefs](#konnectentitystatuswithcontrolplaneandservicerefs)
- [KonnectEntityStatusWithControlPlaneAndUpstreamRefs](#konnectentitystatuswithcontrolplaneandupstreamrefs)
- [KonnectEntityStatusWithControlPlaneRef](#konnectentitystatuswithcontrolplaneref)
- [KonnectEntityStatusWithNetworkRef](#konnectentitystatuswithnetworkref)
- [KonnectGatewayControlPlaneStatus](#konnectgatewaycontrolplanestatus)











#### KonnectEntityStatusWithControlPlaneRef


KonnectEntityStatusWithControlPlaneRef represents the status of a Konnect entity with a reference to a ControlPlane.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this Route is associated with. |


_Appears in:_
- [KonnectCloudGatewayDataPlaneGroupConfigurationStatus](#konnectcloudgatewaydataplanegroupconfigurationstatus)

#### KonnectEntityStatusWithNetworkRef


KonnectEntityStatusWithNetworkRef represents the status of a Konnect entity with reference to a Konnect cloud gateway network.



| Field | Description |
| --- | --- |
| `id` _string_ | ID is the unique identifier of the Konnect entity as assigned by Konnect API. If it's unset (empty string), it means the Konnect entity hasn't been created yet. |
| `serverURL` _string_ | ServerURL is the URL of the Konnect server in which the entity exists. |
| `organizationID` _string_ | OrgID is ID of Konnect Org that this entity has been created in. |
| `networkID` _string_ | NetworkID is the Konnect ID of the Konnect cloud gateway network this entity is associated with. |


_Appears in:_
- [KonnectCloudGatewayTransitGatewayStatus](#konnectcloudgatewaytransitgatewaystatus)

#### KonnectExtensionClientAuth


KonnectExtensionClientAuth contains the configuration for the client authentication for the DataPlane.
At the moment authentication is only supported through client certificate, but it might be extended in the future,
with e.g., token-based authentication.



| Field | Description |
| --- | --- |
| `certificateSecret` _[CertificateSecret](#certificatesecret)_ | CertificateSecret contains the information to access the client certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionClusterType
_Underlying type:_ `string`

KonnectExtensionClusterType is the type of the Konnect Control Plane.





_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionControlPlane


KonnectExtensionControlPlane is the configuration for the Konnect Control Plane.



| Field | Description |
| --- | --- |
| `ref` _[ControlPlaneRef](#controlplaneref)_ | Ref is a reference to a Konnect ControlPlane this KonnectExtension is associated with. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionControlPlaneStatus


KonnectExtensionControlPlaneStatus contains the Konnect Control Plane status information.



| Field | Description |
| --- | --- |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this KonnectExtension is associated with. |
| `clusterType` _[KonnectExtensionClusterType](#konnectextensionclustertype)_ | ClusterType is the type of the Konnect Control Plane. |
| `endpoints` _[KonnectEndpoints](#konnectendpoints)_ | Endpoints defines the Konnect endpoints for the control plane. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### KonnectExtensionDataPlane


KonnectExtensionDataPlane is the configuration for the Konnect DataPlane.



| Field | Description |
| --- | --- |
| `labels` _object (keys:string, values:[DataPlaneLabelValue](#dataplanelabelvalue))_ | Labels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionKonnectSpec


KonnectExtensionKonnectSpec holds the konnect-related configuration.



| Field | Description |
| --- | --- |
| `controlPlane` _[KonnectExtensionControlPlane](#konnectextensioncontrolplane)_ | ControlPlane is the configuration for the Konnect Control Plane. |
| `dataPlane` _[KonnectExtensionDataPlane](#konnectextensiondataplane)_ | DataPlane is the configuration for the Konnect DataPlane. |
| `configuration` _[KonnectConfiguration](#konnectconfiguration)_ | Configuration holds the information needed to set up the Konnect Configuration. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `konnect` _[KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)_ | Konnect holds the konnect-related configuration |
| `clientAuth` _[KonnectExtensionClientAuth](#konnectextensionclientauth)_ | ClientAuth is the configuration for the client certificate authentication. In case the ControlPlaneRef is of type KonnectID, it is required to set up the connection with the Konnect Platform. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### KonnectGatewayControlPlaneSpec


KonnectGatewayControlPlaneSpec defines the desired state of KonnectGatewayControlPlane.



| Field | Description |
| --- | --- |
| `name` _string_ | The name of the control plane. |
| `description` _string_ | The description of the control plane in Konnect. |
| `cluster_type` _[CreateControlPlaneRequestClusterType](#createcontrolplanerequestclustertype)_ | The ClusterType value of the cluster associated with the Control Plane. |
| `auth_type` _[AuthType](#authtype)_ | The auth type value of the cluster associated with the Runtime Group. |
| `cloud_gateway` _boolean_ | Whether this control-plane can be used for cloud-gateways. |
| `proxy_urls` _ProxyURL array_ | Array of proxy URLs associated with reaching the data-planes connected to a control-plane. |
| `labels` _object (keys:string, values:string)_ | Labels store metadata of an entity that can be used for filtering an entity list or for searching across entity types.<br /><br /> Keys must be of length 1-63 characters, and cannot start with "kong", "konnect", "mesh", "kic", or "_". |
| `mirror` _[MirrorSpec](#mirrorspec)_ | Mirror is the Konnect Mirror configuration. It is only applicable for ControlPlanes that are created as Mirrors. |
| `source` _[EntitySource](#entitysource)_ | Source represents the source type of the Konnect entity. |
| `members` _[LocalObjectReference](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#localobjectreference-v1-core) array_ | Members is a list of references to the KonnectGatewayControlPlaneMembers that are part of this control plane group. Only applicable for ControlPlanes that are created as groups. |
| `konnect` _[KonnectConfiguration](#konnectconfiguration)_ | KonnectConfiguration contains the Konnect configuration for the control plane. |


_Appears in:_
- [KonnectGatewayControlPlane](#konnectgatewaycontrolplane)



#### KonnectTransitGatewayAPISpec


KonnectTransitGatewayAPISpec specifies a transit gateway on the Konnect side.
The type and all the types it referenced are mostly copied github.com/Kong/sdk-konnect-go/models/components.CreateTransitGatewayRequest.



| Field | Description |
| --- | --- |
| `type` _[TransitGatewayType](#transitgatewaytype)_ | Type is the type of the Konnect transit gateway. |
| `awsTransitGateway` _[AWSTransitGateway](#awstransitgateway)_ | AWSTransitGateway is the configuration of an AWS transit gateway. Used when type is "AWS Transit Gateway". |
| `azureTransitGateway` _[AzureTransitGateway](#azuretransitgateway)_ | AzureTransitGateway is the configuration of an Azure transit gateway. Used when type is "Azure Transit Gateway". |


_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)

#### MirrorKonnect


MirrorKonnect contains the Konnect Mirror configuration.



| Field | Description |
| --- | --- |
| `id` _[KonnectIDType](#konnectidtype)_ | ID is the ID of the Konnect entity. It can be set only in case the ControlPlane type is Mirror. |


_Appears in:_
- [MirrorSpec](#mirrorspec)

#### MirrorSpec


MirrorSpec contains the Konnect Mirror configuration.



| Field | Description |
| --- | --- |
| `konnect` _[MirrorKonnect](#mirrorkonnect)_ | Konnect contains the KonnectID of the KonnectGatewayControlPlane that is mirrored. |


_Appears in:_
- [KonnectGatewayControlPlaneSpec](#konnectgatewaycontrolplanespec)

#### ProvisioningMethod
_Underlying type:_ `string`

ProvisioningMethod is the type of the provisioning methods available to provision the certificate.





_Appears in:_
- [CertificateSecret](#certificatesecret)

#### SecretRef


SecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [CertificateSecret](#certificatesecret)
- [DataPlaneClientAuthStatus](#dataplaneclientauthstatus)

#### TransitGatewayDNSConfig


TransitGatewayDNSConfig is the DNS configuration of a tansit gateway.



| Field | Description |
| --- | --- |
| `remote_dns_server_ip_addresses` _string array_ | RemoteDNSServerIPAddresses is the list of remote DNS server IP Addresses to connect to for resolving internal DNS via a transit gateway. |
| `domain_proxy_list` _string array_ | DomainProxyList is the list of internal domain names to proxy for DNS resolution from the listed remote DNS server IP addresses, for a transit gateway. |


_Appears in:_
- [AWSTransitGateway](#awstransitgateway)
- [AzureTransitGateway](#azuretransitgateway)

#### TransitGatewayType
_Underlying type:_ `string`

TransitGatewayType defines the type of Konnect transit gateway.





_Appears in:_
- [KonnectCloudGatewayTransitGatewaySpec](#konnectcloudgatewaytransitgatewayspec)
- [KonnectTransitGatewayAPISpec](#konnecttransitgatewayapispec)


## konnect.konghq.com/v1alpha2

Package v1alpha2 contains API Schema definitions for the konnect.konghq.com v1alpha2 API group.

- [KonnectExtension](#konnectextension)
### KonnectExtension


KonnectExtension is the Schema for the KonnectExtension API, and is intended to be referenced as
extension by the DataPlane, ControlPlane or GatewayConfiguration APIs.
If one of the above mentioned resources successfully refers a KonnectExtension, the underlying
deployment(s) spec gets customized to include the konnect-related configuration.

<!-- konnect_extension description placeholder -->

| Field | Description |
| --- | --- |
| `apiVersion` _string_ | `konnect.konghq.com/v1alpha2`
| `kind` _string_ | `KonnectExtension`
| `metadata` _[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.33/#objectmeta-v1-meta)_ | Refer to Kubernetes API documentation for fields of `metadata`. |
| `spec` _[KonnectExtensionSpec](#konnectextensionspec)_ | Spec is the specification of the KonnectExtension resource. |



### Types

In this section you will find types that the CRDs rely on.
#### CertificateSecret


CertificateSecret contains the information to access the client certificate.



| Field | Description |
| --- | --- |
| `provisioning` _[ProvisioningMethod](#provisioningmethod)_ | Provisioning is the method used to provision the certificate. It can be either Manual or Automatic. In case manual provisioning is used, the certificate must be provided by the user. In case automatic provisioning is used, the certificate will be automatically generated by the system. |
| `secretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionClientAuth](#konnectextensionclientauth)

#### DataPlaneClientAuthStatus


DataPlaneClientAuthStatus contains the status information related to the ClientAuth configuration.



| Field | Description |
| --- | --- |
| `certificateSecretRef` _[SecretRef](#secretref)_ | CertificateSecretRef is the reference to the Secret containing the client certificate. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### DataPlaneLabelValue
_Underlying type:_ `string`

DataPlaneLabelValue is the type that defines the value of a label that will be applied to the Konnect DataPlane.





_Appears in:_
- [KonnectExtensionDataPlane](#konnectextensiondataplane)

#### KonnectEndpoints


KonnectEndpoints defines the Konnect endpoints for the control plane.



| Field | Description |
| --- | --- |
| `telemetry` _string_ | TelemetryEndpoint is the endpoint for telemetry. |
| `controlPlane` _string_ | ControlPlaneEndpoint is the endpoint for the control plane. |


_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionClientAuth


KonnectExtensionClientAuth contains the configuration for the client authentication for the DataPlane.
At the moment authentication is only supported through client certificate, but it might be extended in the future,
with e.g., token-based authentication.



| Field | Description |
| --- | --- |
| `certificateSecret` _[CertificateSecret](#certificatesecret)_ | CertificateSecret contains the information to access the client certificate. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionClusterType
_Underlying type:_ `string`

KonnectExtensionClusterType is the type of the Konnect Control Plane.





_Appears in:_
- [KonnectExtensionControlPlaneStatus](#konnectextensioncontrolplanestatus)

#### KonnectExtensionControlPlane


KonnectExtensionControlPlane is the configuration for the Konnect Control Plane.



| Field | Description |
| --- | --- |
| `ref` _[KonnectExtensionControlPlaneRef](#konnectextensioncontrolplaneref)_ | Ref is a reference to a Konnect ControlPlane this KonnectExtension is associated with. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionControlPlaneStatus


KonnectExtensionControlPlaneStatus contains the Konnect Control Plane status information.



| Field | Description |
| --- | --- |
| `controlPlaneID` _string_ | ControlPlaneID is the Konnect ID of the ControlPlane this KonnectExtension is associated with. |
| `clusterType` _[KonnectExtensionClusterType](#konnectextensionclustertype)_ | ClusterType is the type of the Konnect Control Plane. |
| `endpoints` _[KonnectEndpoints](#konnectendpoints)_ | Endpoints defines the Konnect endpoints for the control plane. |


_Appears in:_
- [KonnectExtensionStatus](#konnectextensionstatus)

#### KonnectExtensionDataPlane


KonnectExtensionDataPlane is the configuration for the Konnect DataPlane.



| Field | Description |
| --- | --- |
| `labels` _object (keys:string, values:[DataPlaneLabelValue](#dataplanelabelvalue))_ | Labels is a set of labels that will be applied to the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)

#### KonnectExtensionKonnectSpec


KonnectExtensionKonnectSpec holds the konnect-related configuration.



| Field | Description |
| --- | --- |
| `controlPlane` _[KonnectExtensionControlPlane](#konnectextensioncontrolplane)_ | ControlPlane is the configuration for the Konnect Control Plane. |
| `dataPlane` _[KonnectExtensionDataPlane](#konnectextensiondataplane)_ | DataPlane is the configuration for the Konnect DataPlane. |


_Appears in:_
- [KonnectExtensionSpec](#konnectextensionspec)

#### KonnectExtensionSpec


KonnectExtensionSpec defines the desired state of KonnectExtension.



| Field | Description |
| --- | --- |
| `konnect` _[KonnectExtensionKonnectSpec](#konnectextensionkonnectspec)_ | Konnect holds the konnect-related configuration |
| `clientAuth` _[KonnectExtensionClientAuth](#konnectextensionclientauth)_ | ClientAuth is the configuration for the client certificate authentication. |


_Appears in:_
- [KonnectExtension](#konnectextension)



#### ProvisioningMethod
_Underlying type:_ `string`

ProvisioningMethod is the type of the provisioning methods available to provision the certificate.





_Appears in:_
- [CertificateSecret](#certificatesecret)

#### SecretRef


SecretRef contains the reference to the Secret containing the Konnect Control Plane's cluster certificate.



| Field | Description |
| --- | --- |
| `name` _string_ | Name is the name of the Secret containing the Konnect Control Plane's cluster certificate. |


_Appears in:_
- [CertificateSecret](#certificatesecret)
- [DataPlaneClientAuthStatus](#dataplaneclientauthstatus)

